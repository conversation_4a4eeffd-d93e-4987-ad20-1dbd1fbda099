#!/usr/bin/env python3
"""
probe_proxies_ipapi_debug.py

Same behavior as before but with extra diagnostics:
 - TCP connect test to the proxy
 - normal request to http://ip-api.com/json
 - if DNS/Cloudflare error: try numeric-IP fallback with Host header
 - prints full response / error info

Install:
    pip install requests
    pip install requests[socks]   # only if you may use socks proxies
"""
import requests, socket, time, random, json
from typing import Dict, List
import statistics

RAW_URL = "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/countries/CA/data.json"
# Using HTTP version of api.myip.com for HTTP proxy compatibility
IP_CHECK_URL = "http://api.myip.com/"
MAX_PROXIES = 5
REQUESTS_PER_PROXY = 3
REQUEST_TIMEOUT = 10
SLEEP_BETWEEN_REQUESTS = 0.6
RANDOM_JITTER = 0.15
TCP_CONNECT_TIMEOUT = 5.0

def fetch_proxy_list(url: str) -> List[Dict]:
    print("[info] downloading proxy list...")
    r = requests.get(url, timeout=15)
    r.raise_for_status()
    data = r.json()
    if not isinstance(data, list):
        raise RuntimeError("Expected top-level JSON array")
    print(f"[info] fetched {len(data)} entries")
    return data

def filter_elite(entries: List[Dict]) -> List[Dict]:
    return [e for e in entries if str(e.get("anonymity","")).strip().lower()=="elite"]

def normalize_proxy_url(entry: Dict) -> str:
    # prefer provided 'proxy' field; else assemble from protocol/ip/port
    proxy_field = entry.get("proxy")
    if proxy_field:
        return proxy_field.strip()
    proto = (entry.get("protocol") or "http").lower()
    ip = entry.get("ip")
    port = entry.get("port")
    if not ip or not port:
        raise ValueError("missing ip/port")
    url = f"{proto}://{ip}:{port}"
    # make socks5 -> socks5h to request remote DNS (if socks)
    if url.lower().startswith("socks5://") and "socks5h://" not in url.lower():
        url = url.replace("socks5://", "socks5h://")
    return url

def tcp_check(proxy_ip: str, proxy_port: int) -> bool:
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(TCP_CONNECT_TIMEOUT)
    try:
        s.connect((proxy_ip, int(proxy_port)))
        s.close()
        return True
    except Exception as ex:
        # connection failed
        return False

def get_real_ip() -> str:
    """Get our real IP address without proxy"""
    try:
        r = requests.get(IP_CHECK_URL, timeout=10)
        if r.ok:
            data = r.json()
            return data.get("ip", "unknown")
    except Exception:
        pass
    return "unknown"

def is_proxy_working(proxy_ip: str, real_ip: str) -> bool:
    """Check if the proxy IP is different from our real IP"""
    if proxy_ip == "unknown" or real_ip == "unknown":
        return False
    return proxy_ip != real_ip

def make_request_via_proxy(proxies: Dict[str,str], url: str, headers=None):
    # turn off reading env proxies so system proxy doesn't interfere
    session = requests.Session()
    session.headers.update({"User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Safari/537.36"})
    session.trust_env = False
    try:
        r = session.get(url, proxies=proxies, timeout=REQUEST_TIMEOUT, headers=headers)
        return r, None
    except Exception as ex:
        return None, ex

def probe_proxy(entry: Dict, proxy_url: str, real_ip: str):
    print("\n" + "="*80)
    ip = entry.get("ip")
    port = entry.get("port")
    proto = entry.get("protocol")
    anon = entry.get("anonymity")
    geo = entry.get("geolocation") or {}
    print(f"[proxy] {ip}:{port} proto={proto} anon={anon} geo={geo} proxy_url={proxy_url}")

    # quick TCP connect test to proxy itself:
    can_connect = False
    try:
        can_connect = tcp_check(ip, port)
    except Exception:
        can_connect = False
    print(f"[diag] TCP connect to proxy {ip}:{port} -> {'OK' if can_connect else 'FAILED'} (timeout {TCP_CONNECT_TIMEOUT}s)")

    if not can_connect:
        print("--- summary ---")
        print("TCP connection failed - skipping proxy")
        print("="*80 + "\n")
        return False

    # build proxies dict for requests
    proxies = {"http": proxy_url, "https": proxy_url}

    successes = 0
    durations = []
    proxy_working = False

    for i in range(1, REQUESTS_PER_PROXY+1):
        t0 = time.time()
        r, err = make_request_via_proxy(proxies, IP_CHECK_URL)
        dt = time.time() - t0

        if r is not None and r.ok:
            durations.append(dt)
            try:
                data = r.json()
                proxy_ip = data.get("ip", "unknown")
                country = data.get("country", "unknown")
                cc = data.get("cc", "unknown")

                print(f"[{i}/{REQUESTS_PER_PROXY}] request OK status={r.status_code} time={dt:.2f}s")
                print(f"  Proxy IP: {proxy_ip} (Country: {country}/{cc})")
                print(f"  Real IP: {real_ip}")

                if is_proxy_working(proxy_ip, real_ip):
                    print(f"  ✓ PROXY WORKING - IP changed from {real_ip} to {proxy_ip}")
                    proxy_working = True
                    successes += 1
                else:
                    print(f"  ✗ PROXY NOT WORKING - IP unchanged ({proxy_ip})")

            except Exception as ex:
                print(f"[{i}/{REQUESTS_PER_PROXY}] request OK but JSON parse failed: {ex}")
                print(f"  Response: {r.text[:200]}")
        else:
            print(f"[{i}/{REQUESTS_PER_PROXY}] request FAILED after {dt:.2f}s -> {repr(err) if err else 'HTTP error'}")

        # polite delay
        time.sleep(max(0, SLEEP_BETWEEN_REQUESTS + random.uniform(-RANDOM_JITTER, RANDOM_JITTER)))

    # summary
    print("--- summary ---")
    print(f"successful requests: {successes}/{REQUESTS_PER_PROXY}")
    print(f"proxy working: {'YES' if proxy_working else 'NO'}")
    if durations:
        print(f"RTT(s): min={min(durations):.2f} avg={statistics.mean(durations):.2f} max={max(durations):.2f}")
    else:
        print("no successful requests recorded")
    print("="*80 + "\n")

    return proxy_working

def main():
    print("[info] Getting real IP address...")
    real_ip = get_real_ip()
    print(f"[info] Real IP: {real_ip}")

    if real_ip == "unknown":
        print("[error] Could not determine real IP address")
        return

    all_entries = fetch_proxy_list(RAW_URL)
    elite = filter_elite(all_entries)
    if not elite:
        print("[error] no elite proxies found")
        return

    random.shuffle(elite)
    chosen = elite[:MAX_PROXIES]
    print(f"[info] probing {len(chosen)} proxies (each up to {REQUESTS_PER_PROXY} requests)")

    working_proxies = []

    for idx, entry in enumerate(chosen, start=1):
        try:
            proxy_url = normalize_proxy_url(entry)
        except Exception as ex:
            print(f"[warn] skipping proxy #{idx} due to error: {ex}")
            continue
        print(f"\n>> Proxy #{idx}/{len(chosen)}")
        if probe_proxy(entry, proxy_url, real_ip):
            working_proxies.append((entry, proxy_url))

    print(f"\n{'='*80}")
    print(f"FINAL RESULTS: {len(working_proxies)} working proxies found out of {len(chosen)} tested")
    for i, (entry, proxy_url) in enumerate(working_proxies, 1):
        ip = entry.get("ip")
        port = entry.get("port")
        geo = entry.get("geolocation", {})
        country = geo.get("country", "unknown")
        city = geo.get("city", "unknown")
        print(f"  {i}. {proxy_url} ({country}/{city})")
    print("="*80)

if __name__ == "__main__":
    import random, statistics
    main()
