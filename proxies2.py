#!/usr/bin/env python3
"""
probe_proxies_ipapi_proxifly_format.py

- Fetches a JSON proxy list (expected structure shown by user)
- Keeps only entries where anonymity == "elite"
- Uses up to MAX_PROXIES proxies; for each does REQUESTS_PER_PROXY requests to http://ip-api.com/json
- Prints each ip-api response and a summary per-proxy.

Run:
    python probe_proxies_ipapi_proxifly_format.py
"""

import requests
import time
import json
import random
import statistics
from typing import List, Dict

RAW_URL = "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/countries/CA/data.json"
IP_API = "http://ip-api.com/json"
MAX_PROXIES = 10
REQUESTS_PER_PROXY = 2
REQUEST_TIMEOUT = 10         # seconds
SLEEP_BETWEEN_REQUESTS = 0.5  # polite delay (seconds)
RANDOM_JITTER = 0.15         # small jitter added to sleep

def fetch_proxy_list(url: str) -> List[Dict]:
    print(f"[info] downloading proxy list from {url}")
    r = requests.get(url, timeout=15)
    r.raise_for_status()
    data = r.json()
    if not isinstance(data, list):
        raise RuntimeError("Expected top-level JSON array of proxies")
    print(f"[info] downloaded {len(data)} entries")
    return data

def filter_elite(entries: List[Dict]) -> List[Dict]:
    out = [e for e in entries if str(e.get("anonymity", "")).strip().lower() == "elite"]
    print(f"[info] filtered to {len(out)} elite proxies")
    return out

def normalize_proxy_url(entry: Dict) -> str:
    """
    Prefer using entry['proxy'] if present. Otherwise compose from protocol, ip and port.
    For socks5, ensure remote DNS resolution by using socks5h scheme if possible.
    """
    proxy_field = entry.get("proxy")
    proto = str(entry.get("protocol", "")).lower() if entry.get("protocol") is not None else ""
    ip = str(entry.get("ip", "")).strip()
    port = entry.get("port", "")
    if proxy_field:
        proxy_url = proxy_field.strip()
    else:
        if not ip or not port:
            raise ValueError("Missing proxy and missing ip/port")
        proxy_url = f"{proto}://{ip}:{port}" if proto else f"http://{ip}:{port}"

    # If it's socks5 and not using the 'h' variant, switch to socks5h to force remote DNS (avoid DNS leaks)
    lower = proxy_url.lower()
    if "socks5://" in lower and "socks5h://" not in lower:
        proxy_url = proxy_url.replace("socks5://", "socks5h://")
    # similarly convert socks4? (socks4 doesn't support remote DNS in the same way)
    return proxy_url

def build_requests_proxies(proxy_url: str) -> Dict[str,str]:
    """
    Build a requests-compatible proxies dict for both http and https.
    Many HTTP proxies support CONNECT for HTTPS, so pointing https->same proxy is common.
    """
    return {"http": proxy_url, "https": proxy_url}

def pretty_json(obj):
    try:
        return json.dumps(obj, indent=2, ensure_ascii=False)
    except Exception:
        return str(obj)

def probe_proxy(entry: Dict, proxy_url: str):
    print("\n" + "="*70)
    ip = entry.get("ip", "")
    port = entry.get("port", "")
    proto = entry.get("protocol", "")
    anon = entry.get("anonymity", "")
    geo = entry.get("geolocation", {}) or {}
    country = geo.get("country", "") or entry.get("country", "")
    city = geo.get("city", "") or ""
    score = entry.get("score", "")
    print(f"[proxy] {ip}:{port}  protocol={proto} anonymity={anon} country={country} city={city} score={score}")
    proxies = build_requests_proxies(proxy_url)
    successes = 0
    durations = []

    for i in range(1, REQUESTS_PER_PROXY+1):
        t0 = time.time()
        try:
            r = requests.get(IP_API, proxies=proxies, timeout=REQUEST_TIMEOUT)
            dt = time.time() - t0
            durations.append(dt)
            status = r.status_code
            # ip-api returns JSON typically
            try:
                j = r.json()
                print(f"[{i}/{REQUESTS_PER_PROXY}] {dt:.2f}s status={status} response=\n{pretty_json(j)}")
            except Exception:
                # fallback to raw text snippet
                text = r.text[:1000]
                print(f"[{i}/{REQUESTS_PER_PROXY}] {dt:.2f}s status={status} non-json response (snippet):\n{text}")
            if r.ok:
                successes += 1
        except Exception as e:
            dt = time.time() - t0
            print(f"[{i}/{REQUESTS_PER_PROXY}] {dt:.2f}s request failed: {repr(e)}")
        # polite delay with small jitter to reduce chance of rate limits
        sleep_for = SLEEP_BETWEEN_REQUESTS + random.uniform(-RANDOM_JITTER, RANDOM_JITTER)
        if sleep_for > 0:
            time.sleep(sleep_for)

    # Summary
    print("--- proxy summary ---")
    print("Proxy URL:", proxy_url)
    print(f"Requests succeeded: {successes}/{REQUESTS_PER_PROXY}")
    if durations:
        print(f"RTT (s): min={min(durations):.2f}, avg={statistics.mean(durations):.2f}, max={max(durations):.2f}")
    else:
        print("No successful requests to compute RTT")
    print("="*70 + "\n")

def main():
    all_entries = fetch_proxy_list(RAW_URL)
    elite = filter_elite(all_entries)
    if not elite:
        print("[error] no elite proxies found - exiting")
        return

    # shuffle so we get a random selection
    # random.shuffle(elite)
    chosen = elite[:MAX_PROXIES]
    print(f"[info] probing {len(chosen)} proxies (up to {REQUESTS_PER_PROXY} requests each)")

    for idx, entry in enumerate(chosen, start=1):
        try:
            proxy_url = normalize_proxy_url(entry)
        except Exception as e:
            print(f"[warn] skipping entry #{idx} due to normalization error: {e}")
            continue
        print(f"\n>> Proxy #{idx}/{len(chosen)}: using proxy_url={proxy_url}")
        probe_proxy(entry, proxy_url)

    print("[done] all proxies processed")

if __name__ == "__main__":
    main()
