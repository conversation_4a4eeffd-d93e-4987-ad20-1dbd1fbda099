<!DOCTYPE html>
    <html lang="en" class="overflow-y-scroll">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Ottawa MLS&reg; Listings &amp; Real Estate for Sale &#124; Zolo.ca</title>
    <meta name="description" content="6340 homes for sale in Ottawa ON &ndash; See photos of new Ottawa Real Estate &amp; MLS Listings &bull; Open Houses &ndash; Faster than MLS.ca &amp; updated every 15 mins!" />


    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());


gtag('config', 'G-SRQSX6WNCC', {
    'content_group': 'Gallery - City'
});


</script>



    <script>
    var ZOLO_DATA = {
        appuuid: "",
        pageAction: "index",
        isListingPage: false,
        isResidentialProperty: false,
        sarea: "Ottawa",
        mapArea: "",
        propertyId: "",
        propertyLat: "",
        propertyLng: "",
        searchCity: "ottawa",
        searchNeighborhood: "",
        hasVirtualTour: false,
        tourIsHttpsEnabled: false,
        customFilterSearch: "",
        isCommercialSearch: false,
        appStoreRedirect: "https:\/\/www.zolo.ca\/app",
        vapid: "BPCZ6vKo_x0kSvP4iMOIvn9EjmY4s8DgfsNRR63cQAxsJvODh84KGjJCaae7E1YruFXb4khIgNAMoJyU4KWTBrg",
        firebaseKey: "AIzaSyB3AV3Rt23lN_4La_M_AULJtYMxMMK5wYM",
        firebaseProjectId: "qh-zolo-notify-prod-1n",
        firebaseSenderId: "************",
        firebaseAppId: "1:************:web:5ed0f5cb0afc209f534fee"
    }
    </script>

    <script>
        const SR_TYPE_SALE = 1;
const SR_TYPE_LEASE = 2;
const SR_TYPE_SOLD = 3;
const SR_ALLOW_TYPE = [1,2,3];
const SEARCH_ORDER_RECOMMENDED = 0;
const SEARCH_ORDER_MOST_RECENT = 3;
const SEARCH_ORDER_OLDEST = 4;
const SEARCH_ORDER_MOST_EXPENSIVE = 1;
const SEARCH_ORDER_LEAST_EXPENSIVE = 2;
const SEARCH_ORDER_MOST_BEDROOMS = 5;
    </script>

        <script>
        window.lazySizesConfig = window.lazySizesConfig || {};
        window.lazySizesConfig.lazyClass = 'js-img-defer';
        window.lazySizesConfig.ricTimeout = 1000;
        window.lazySizesConfig.throttleDelay = 200;
        window.lazySizesConfig.loadMode = 1;
        lazySizesConfig.srcAttr = 'data-img-defer-src';
                var tld = "https://www.zolo.ca/";
        var buyeruuid = "15887706-7524-11f0-a74b-42010a218d04";
        var gm_api = "";
        var gm_key = "AIzaSyABu08NpIVy1fOcY76ayUWbxEtaMX0tAWY";
        var streetview_key = "AIzaSyCMGBNWYQfItHL0QfIv3016Des5SxMOlfU";
        var action = "index";
    </script>

        <script>
        (function (b) { var c = function (c, a, f) { if (void 0 === a) return c = ("; " + b.document.cookie).split("; " + c + "="), 2 === c.length ? c.pop().split(";").shift() : null; !1 === a && (f = -1); var d = ""; f && (d = new Date, d.setTime(d.getTime() + 864E5 * f), d = "; expires=" + d.toGMTString()); b.document.cookie = c + "=" + a + d + "; path=/" }; "undefined" != typeof module ? module.exports = c : b.cookie = c })("undefined" != typeof global ? global : this); cookie("full-css") || cookie("full-css", !0, 7);
    </script>

        <script>
        var ZOLO = { appuuid: function () { return ZOLO_DATA.appuuid }, pageAction: function () { return ZOLO_DATA.pageAction }, isListingPage: function () { return ZOLO_DATA.isListingPage }, isResidentialProperty: function () { return ZOLO_DATA.isResidentialProperty }, sarea: function () { return ZOLO_DATA.sarea }, mapArea: function () { return ZOLO_DATA.mapArea }, propertyId: function () { return ZOLO_DATA.propertyId }, propertyLat: function () { return ZOLO_DATA.propertyLat }, propertyLng: function () { return ZOLO_DATA.propertyLng } ,searchCity: function () { return ZOLO_DATA.searchCity } ,searchNeighborhood: function () { return ZOLO_DATA.searchNeighborhood }, hasVirtualTour: function () { return ZOLO_DATA.hasVirtualTour }, tourIsHttpsEnabled: function () { return ZOLO_DATA.tourIsHttpsEnabled }, customFilterSearch: function () { return ZOLO_DATA.customFilterSearch }, isCommercialSearch: function () { return ZOLO_DATA.isCommercialSearch }, appStoreRedirect: function () { return ZOLO_DATA.appStoreRedirect }, vapid: function () { return ZOLO_DATA.vapid }, firebaseKey: function () { return ZOLO_DATA.firebaseKey }, firebaseProjectId: function () { return ZOLO_DATA.firebaseProjectId }, firebaseSenderId: function () { return ZOLO_DATA.firebaseSenderId }, firebaseAppId: function () { return ZOLO_DATA.firebaseAppId}};
        var MOBILEDETECT = { orientation: 0, isMobile: false, isiOS: false, zoloMobile: !1, readDeviceOrientation: function () { window.innerWidth > window.innerHeight ? MOBILEDETECT.orientation = 1 : MOBILEDETECT.orientation = 0, MOBILEDETECT.checkZoloMobile() }, checkZoloMobile: function () { MOBILEDETECT.isMobile && (MOBILEDETECT.isiOS && 0 === MOBILEDETECT.orientation || !MOBILEDETECT.isiOS && window.innerWidth < 1024 || MOBILEDETECT.isiOS && 1 === MOBILEDETECT.orientation && window.innerWidth < 1024) ? (!1 !== MOBILEDETECT.zoloMobile || ZOLO.isListingPage() || document.getElementById("sarea") instanceof HTMLElement && (document.getElementById("mobile_sarea").value = document.getElementById("sarea").value), MOBILEDETECT.zoloMobile = !0) : (!0 === MOBILEDETECT.zoloMobile && document.getElementById("sarea") instanceof HTMLElement && (document.getElementById("sarea").value = document.getElementById("mobile_sarea").value), MOBILEDETECT.zoloMobile = !1) }, isZoloMobile: function () { return MOBILEDETECT.zoloMobile } }; MOBILEDETECT.readDeviceOrientation(), window.addEventListener("resize", function () { MOBILEDETECT.readDeviceOrientation() });
        if (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) { MOBILEDETECT.isMobile = true; }//ios 13 ipad detect
    </script>


    <style>
        @font-face {
            font-family: RobotoFull;
            src: local('Roboto'), local('Roboto-Regular'), url(/font/Roboto-Regular-Sub.woff2?v=1.5) format('woff2'), url(/font/Roboto-Regular-Sub.woff?v=1.5) format('woff');
            font-weight: 400;
            font-display: swap
        }

        @font-face {
            font-family: RobotoFull;
            src: local('Roboto Medium'), local('Roboto-Medium'), url(/font/Roboto-Medium-Sub.woff2?v=1.5) format('woff2'), url(/font/Roboto-Medium-Sub.woff?v=1.5) format('woff');
            font-weight: 500;
            font-display: swap
        }

        @font-face {
            font-family: RobotoFull;
            src: local('Roboto Bold'), local('Roboto-Bold'), url(/font/Roboto-Bold-Sub.woff2?v=1.5) format('woff2'), url(/font/Roboto-Bold-Sub.woff?v=1.5) format('woff');
            font-weight: 700;
            font-display: swap
        }

        @font-face {
            font-family: zoloicons;
            src: url(/font/zoloicons.woff2?v=1.7) format("woff2"), url(/font/zoloicons.woff?v=1.7) format("woff");
            font-weight: 400;
            font-display: block
        }

        body {
            font-family: RobotoFull, sans-serif;
        }
    </style>

                <style>
            html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}main{display:block}h1{font-size:2em;margin:.67em 0}hr{box-sizing:content-box;height:0;overflow:visible}strong{font-weight:bolder}img{border-style:none}button,input,select{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button,input{overflow:visible}button,select{text-transform:none}[type=submit],button{-webkit-appearance:button}form,label{border:0;font-size:100%;vertical-align:baseline}form,h1,h3,h4,label,li,p,ul{margin:0;padding:0;font:inherit}img{max-width:100%;height:auto}iframe{border:0}input{-webkit-appearance:none;border-radius:0}html{font-size:16px}body{font-size:1rem;line-height:1.5}h1{font-size:1.75rem}h1,h3{line-height:1.2}h3{font-size:1.125rem}h4{font-size:1rem;line-height:1.3}a{text-decoration:none;color:#008fd5}strong{font-weight:500}ul{font-variant-numeric:tabular-nums;font-feature-settings:"tnum" 1;padding-left:2rem}.button{background-color:transparent!important;background-image:none!important;white-space:nowrap!important;-webkit-appearance:none;appearance:none;font-family:inherit!important;padding:.5rem 1rem!important;font-size:1rem!important;line-height:1.5rem!important;border-radius:.5rem!important;text-decoration:none!important;display:inline-block!important;text-align:center!important;background-color:#008fd5!important;color:#fff!important;border:1px solid transparent!important;outline:0!important}.button--menu{background-color:transparent!important;color:rgba(0,0,0,.87)!important;border-color:transparent!important;font-size:.875rem!important;font-weight:500!important}.button--mono,.button--white-black{background-color:#fff!important;color:rgba(0,0,0,.87)!important;border:1px solid rgba(0,0,0,.1)!important}.button--small{padding:.5rem .875rem!important;font-size:.875rem!important;line-height:1rem!important}.button--large{padding:1rem 1.5rem!important}.text-input{font-family:inherit!important;background:#fff!important;font-size:1rem!important;line-height:1.5rem!important;padding:.5rem .75rem!important;border:1px solid rgba(0,0,0,.1)!important}select::-ms-expand{display:none}.xs-col-6{width:50%!important}.text-2{font-size:1.375rem!important;line-height:1.2!important}.text-5{font-size:.875rem!important}.text-5,.text-6{line-height:1.3!important}.text-6{font-size:.75rem!important}.normal{font-weight:400!important}.bold{font-weight:500!important}.heavy{font-weight:700!important}.xs-text-left{text-align:left!important}.xs-text-center{text-align:center!important}.nowrap,.truncate{white-space:nowrap!important}.truncate{overflow:hidden!important;text-overflow:ellipsis!important}.decoration-underline{text-decoration:underline!important;text-underline-offset:.1em}.list-unstyled{margin-left:0!important;padding-left:0!important;list-style:none!important}.rounded{border-radius:.5rem!important}.circle{border-radius:50%!important}.xs-rounded-md{border-radius:.5rem!important}.xs-border{border:1px solid rgba(0,0,0,.1)!important}.xs-border-top{border-top:1px solid rgba(0,0,0,.1)!important}.xs-border-none{border:none!important}@media (min-width:64rem){.md-border{border:1px solid rgba(0,0,0,.1)!important}.md-border-bottom-none{border-bottom:none!important}}.xs-overflow-hidden{overflow:hidden!important}.xs-hide{display:none!important}.xs-inline{display:inline!important}.xs-block{display:block!important}.xs-inline-block{display:inline-block!important}.xs-flex{display:flex!important}.xs-inline-flex{display:inline-flex!important}.xs-grid{display:grid!important}.xs-inline-grid{display:inline-grid!important}.xs-full-height{height:100%!important}.xs-width-auto{width:auto!important}.xs-full-width{width:100%!important}.xs-max-h-full{max-height:100%!important}@media (min-width:48rem){.sm-inline{display:inline!important}.sm-block{display:block!important}}@media (min-width:64rem){.md-hide{display:none!important}.md-inline{display:inline!important}.md-block{display:block!important}.md-inline-block{display:inline-block!important}.md-flex{display:flex!important}}.xs-mb0{margin-bottom:0!important}.xs-mr05{margin-right:.25rem!important}.xs-mb05{margin-bottom:.25rem!important}.xs-ml05,.xs-mx05{margin-left:.25rem!important}.xs-mx05{margin-right:.25rem!important}.xs-mt1{margin-top:.5rem!important}.xs-mr1{margin-right:.5rem!important}.xs-mb1{margin-bottom:.5rem!important}.xs-ml1{margin-left:.5rem!important}.xs-my1{margin-top:.5rem!important;margin-bottom:.5rem!important}.xs-mt2{margin-top:1rem!important}.xs-mr2{margin-right:1rem!important}.xs-mb2{margin-bottom:1rem!important}.xs-mt3{margin-top:1.5rem!important}.xs-mr3{margin-right:1.5rem!important}.xs-mb3{margin-bottom:1.5rem!important}.xs-mx3{margin-left:1.5rem!important;margin-right:1.5rem!important}.xs-mr4{margin-right:2rem!important}.xs-mb5{margin-bottom:3rem!important}.xs-mx2-{margin-left:-1rem!important;margin-right:-1rem!important}.xs-mr-auto{margin-right:auto!important}.xs-ml-auto{margin-left:auto!important}@media (min-width:48rem){.sm-mx0{margin-left:0!important;margin-right:0!important}}@media (min-width:64rem){.md-mt0{margin-top:0!important}.md-mr0{margin-right:0!important}.md-ml05{margin-left:.25rem!important}.md-mt1{margin-top:.5rem!important}.md-mr1{margin-right:.5rem!important}.md-ml1,.md-mx1{margin-left:.5rem!important}.md-mx1{margin-right:.5rem!important}.md-ml-auto{margin-left:auto!important}}.xs-p0{padding:0!important}.xs-p05{padding:.25rem!important}.xs-pr05{padding-right:.25rem!important}.xs-pl05{padding-left:.25rem!important}.xs-py05{padding-top:.25rem!important;padding-bottom:.25rem!important}.xs-p1{padding:.5rem!important}.xs-pt1{padding-top:.5rem!important}.xs-pr1,.xs-px1{padding-right:.5rem!important}.xs-px1{padding-left:.5rem!important}.xs-py1{padding-top:.5rem!important;padding-bottom:.5rem!important}.xs-p2{padding:1rem!important}.xs-pl2,.xs-px2{padding-left:1rem!important}.xs-px2{padding-right:1rem!important}.xs-py2{padding-top:1rem!important;padding-bottom:1rem!important}.xs-p3{padding:1.5rem!important}.xs-pt3{padding-top:1.5rem!important}.xs-pb3{padding-bottom:1.5rem!important}.xs-pl3,.xs-px3{padding-left:1.5rem!important}.xs-px3{padding-right:1.5rem!important}.xs-py3{padding-top:1.5rem!important;padding-bottom:1.5rem!important}.xs-pt4{padding-top:2rem!important}.xs-pr4{padding-right:2rem!important}.xs-py4{padding-top:2rem!important;padding-bottom:2rem!important}.xs-pt5{padding-top:3rem!important}.xs-pr5{padding-right:3rem!important}.xs-pb5{padding-bottom:3rem!important}@media (min-width:64rem){.md-p0{padding:0!important}.md-pt0{padding-top:0!important}.md-pb0{padding-bottom:0!important}.md-px0{padding-left:0!important;padding-right:0!important}.md-p1{padding:.5rem!important}.md-py2{padding-top:1rem!important;padding-bottom:1rem!important}.md-pb5{padding-bottom:3rem!important}}.xs-relative{position:relative!important}.xs-absolute{position:absolute!important}.xs-fixed{position:fixed!important}.xs-z1{z-index:100!important}.xs-z2{z-index:200!important}.xs-z3{z-index:300!important}.xs-z4{z-index:400!important}.xs-t0{top:0!important}.xs-r0{right:0!important}.xs-b0{bottom:0!important}.xs-l0{left:0!important}.xs-t05{top:.25rem}.xs-t1{top:.5rem!important}.xs-r1{right:.5rem!important}.xs-l1{left:.5rem!important}.xs-t2{top:1rem!important}.xs-r2{right:1rem!important}.xs-l2{left:1rem!important}.xs-l-auto{left:auto!important}@media (min-width:64rem){.md-relative{position:relative!important}.md-z3{z-index:300!important}}.flex{display:flex}.min-width-0{min-width:0}.xs-flex-order-1{order:1}.xs-flex-order-2{order:2}.xs-flex-order-3{order:3}.xs-flex-grow-1{flex-grow:1}.xs-flex-shrink-0{flex-shrink:0}.xs-flex-column{flex-direction:column}.xs-flex-column-reverse{flex-direction:column-reverse}.xs-flex-wrap{flex-wrap:wrap}.xs-flex-justify-end{justify-content:flex-end}.xs-flex-justify-center{justify-content:center}.xs-flex-justify-space-between{justify-content:space-between}.xs-flex-align-end{align-items:flex-end}.xs-flex-align-center{align-items:center}@media (min-width:48rem){.sm-flex-align-center{align-items:center}}@media (min-width:64rem){.md-flex-order-1{order:1}.md-flex-order-3{order:3}.md-flex-grow-1{flex-grow:1}.md-flex-row{flex-direction:row}.md-flex-justify-center{justify-content:center}.md-flex-align-center{align-items:center}}.fill-green{background-color:#71bf44!important}.fill-green-highlight{background-color:rgba(113,191,68,.075)!important}.fill-orange{background-color:#f36f21!important}.fill-white{background-color:#fff!important}.fill-primary{background-color:rgba(0,0,0,.87)!important}.fill-grey-bg{background-color:#fafafa!important}.fill-grey-bg-2{background-color:#f5f5f5!important}.fill-grey-bg-3{background-color:#eee!important}.fill-current{fill:currentColor}.text-blue{color:#008fd5!important}.text-green{color:#71bf44!important}.text-red{color:#e32!important}.text-white{color:#fff!important}.text-primary{color:rgba(0,0,0,.87)!important}.text-secondary{color:rgba(0,0,0,.54)!important}.text-muted{color:rgba(0,0,0,.38)!important}html{height:100%;box-sizing:border-box}*,:after,:before{box-sizing:inherit}body{max-height:100%;max-width:100%;background-color:#fff;color:rgba(0,0,0,.87)}input[type=email],input[type=tel],input[type=text],select{width:100%!important}p{margin-bottom:1.5rem}hr{display:block;border:0;border-top:1px solid rgba(0,0,0,.05);margin:1em 0}.sr-only,hr{height:1px;padding:0}.sr-only{position:absolute;width:1px;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.xs-text-1{font-size:1.75rem!important;line-height:1.2!important}.xs-text-2{font-size:1.375rem!important;line-height:1.3!important}.xs-text-3{font-size:1.125rem!important;line-height:1.5!important}.xs-text-4{font-size:1rem!important;line-height:1.5rem!important}.xs-text-5{font-size:.875rem!important;line-height:1.25rem!important}.xs-text-6{font-size:.75rem!important;line-height:1rem!important}@media (min-width:48rem){.sm-text-1{font-size:1.75rem!important;line-height:1.2!important}.sm-text-3{font-size:1.125rem!important;line-height:1.5!important}}@media (min-width:64rem){.md-text-5{font-size:.875rem!important;line-height:1.25rem!important}}.xs-line-height-1{line-height:1!important}.xs-shadow-top{box-shadow:0 -1px 12px rgba(0,0,0,.08)}.xs-shadow-bottom{box-shadow:0 1px 12px rgba(0,0,0,.08)}@media (min-width:64rem){.md-shadow-bottom{box-shadow:0 1px 12px rgba(0,0,0,.08)}}.shadow-3{box-shadow:0 6px 1rem rgba(0,0,0,.08)}.shadow-card{box-shadow:0 1px 12px rgba(0,0,0,.08)}.border-transparent{border-color:transparent!important}.pill{border-radius:10rem!important}.xs-grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}@media (min-width:48rem){.sm-grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}}@media (min-width:75rem){.lg-grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}}.xs-gap-1{gap:.5rem}.xs-gap-2{gap:1rem}.xs-gap-5{gap:3rem}@media (min-width:48rem){.sm-gap-3{gap:1.5rem}}.hide{display:none}.overflow-y-scroll{overflow-y:scroll}.overflow-y-auto{overflow-y:auto}.xs-h-40{height:2.5rem}.xs-h-60{height:3.75rem}.xs-h-64{height:4rem}.xs-w-40{width:2.5rem}.xs-w-60{width:3.75rem}@media (min-width:48rem){.sm-max-w-lg{max-width:32rem}}.xs-aspect-3-2{position:relative}.xs-aspect-3-2:before{display:block;content:"";width:100%;padding-top:66.66667%}.aspect-content{position:absolute!important;top:0;right:0;bottom:0;left:0}.object-fit-cover{object-fit:cover}.object-position-t-l{object-position:top left}.gut{padding-left:1.5rem;padding-right:1.5rem}@supports (padding:max(0px)){.gut{padding-left:max(1.5rem,env(safe-area-inset-left));padding-right:max(1.5rem,env(safe-area-inset-right))}}@media only screen and (max-width:63.9375rem){.xs-gut-neg{margin-left:-1.5rem;margin-right:-1.5rem}}.dropdown{position:relative;display:inline-block}.dropdown-menu{position:absolute;top:100%;top:calc(100% + .5rem);left:0;z-index:1000;display:none;float:left;padding:.5rem;background-color:#fff;border-radius:.5rem;border:1px solid rgba(0,0,0,.1)}@media only screen and (min-width:64rem){.dropdown-menu{box-shadow:0 1rem 2rem 2px rgba(0,0,0,.15)}}.dropdown-menu-topnav{display:block;transform:translate(-9999px,-9999px)}.header{height:4rem}@media only screen and (min-width:64rem){.header{height:5rem}}.logo-zolo{height:36px;width:36px}@media only screen and (min-width:64rem){.logo-zolo{height:30px;width:115px}}.nav-actions{min-height:4rem;padding-bottom:env(safe-area-inset-bottom);transform:translateY(0)}.smart-banner{transform:translateY(-110%)}.card-listing--img.no-photo{filter:invert(.5)}#listing_container{position:relative}.loading-overlay{opacity:0;visibility:hidden;background:hsla(0,0%,100%,.9)}@media only screen and (max-width:63.9375rem){.section-search .dropdown{width:100%;float:none;display:block}}@media only screen and (max-width:63.9375rem){.section-search .dropdown-menu{position:relative;display:block;width:100%;border:0;z-index:1}}@media only screen and (max-width:63.9375rem){.drawer{transform:translateX(100%);overflow:hidden;height:auto;width:100%;margin:0;background:#fafafa;position:fixed;top:0;right:0;bottom:0;left:0;z-index:5000;will-change:transform}}.drawer.drawer-ltr{transform:translateX(-100%);width:80%;max-width:300px}.drawer.drawer-bottom-up{transform:translateX(0)}@media only screen and (max-width:63.9375rem){.drawer.drawer-bottom-up{transform:translateY(100%) translateY(88px)}}@media only screen and (max-width:63.9375rem){.drawer-body{position:absolute;top:0;bottom:0;width:100%;overflow-y:auto;-webkit-overflow-scrolling:touch;list-style-type:none}}@media only screen and (max-width:63.9375rem){.drawer-top .drawer-body{top:4rem}}@media only screen and (min-width:64rem){.drawer.drawer-menu{transform:translateX(0);width:auto;max-width:none}}
        </style>

    <link rel="stylesheet" href="/css/screen.css?v=7.6.811" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="/css/screen.css?v=7.6.811" /></noscript>

    <link
        href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700|Roboto+Slab:100,300,400,700|Roboto:300,300i,400,400i,500,500i,700,700i&amp;display=swap"
        media="all" rel="stylesheet" type="text/css">





        <script defer src="/js/minified/lazysizes.js?v=2.6.001"></script>


        <script defer src="/js/minified/siema.min.js?v=2.6.001"></script>
                    <script defer src="/js/minified/jquery.js?v=2.6.001"></script>
                <script defer src="/js/minified/bootstrap.min.js?v=2.6.001"></script>
                    <script defer src="/js/minified/autocomplete.js?v=2.6.001"></script>

                    <script defer src="/js/minified/tocca.js?v=2.6.001"></script>

                <script defer src="/js/minified/waypoint.js?v=2.6.001"></script>
        <script defer src="/js/minified/tooltip.js?v=2.6.001"></script>

                    <script defer src="/js/minified/zolo.js?v=2.6.001"></script>


                                    <script defer src="/js/minified/zolo.search.js?v=2.6.001"></script>






        <script defer src="/js/firebase-app-8.10.1.min.js"></script>
        <script defer src="/js/firebase-messaging-8.10.1.min.js"></script>

        <script defer src="/js/minified/browser_push.js?v=2.6.001"></script>

                    <script defer src="https://www.googletagmanager.com/gtag/js?id=G-SRQSX6WNCC"></script>




    <meta name="google-site-verification" content="2WoQKxEZ6blET4tNwSiGxzXs6gk2JKU_IHWXk-nNlrs" />
    <meta name="HandheldFriendly" content="True" />
    <meta name="MobileOptimized" content="320" />
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="google-play-app" content="app-id=com.ols.zolo">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-title" content="Zolo">
    <meta name="application-name" content="Zolo">
    <meta name="theme-color" content="#ffffff">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png?v=1.5">
    <link rel="manifest" href="/manifest.json?v=1.5" crossorigin="use-credentials">
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="alternate icon" type="image/x-icon" href="/favicon.ico?v=1.5">
    <link rel="mask-icon" href="/safari-pinned-tab.svg?v=1.5" color="#008fd5">
    <link rel="hub" href="https://pubsubhubbub.appspot.com/" />
    <link rel="self" href="https://www.zolo.ca/rss_new_listings.php" type="application/rss+xml" />
    <link rel="next" href="https://www.zolo.ca/ottawa-real-estate/page-2" /><link rel="canonical" href="https://www.zolo.ca/ottawa-real-estate"/><link rel="alternate" href="android-app://com.ols.zolo/https/www.zolo.ca/ottawa-real-estate" />
        <script type="application/ld+json">
[
    {
        "@context":"http://schema.org","@type":"WebSite","name":"Zolo","alternateName":"Zolo.ca","url":"https://www.zolo.ca"
            },
    {
        "@context":"http://schema.org","@type":"Corporation","name":"Zolo","description":"Zolo is one of Canada's most popular national real estate marketplaces. Each month, over 10 million home shoppers use Zolo to level up the way they buy, sell, rent, finance, and learn about real estate.","url":"https://www.zolo.ca","logo":"https://www.zolo.ca/img/zolo-logo-graph.png","telephone":"**************","foundingDate":"2012","founders":[{"@type":"Person","name":"Ryley Best"},{"@type":"Person","name":"Jason Billingsley"},{"@type":"Person","name":"Barry Allen"}],"sameAs":["https://www.facebook.com/zolocanada","https://www.twitter.com/zolocanada","https://www.pinterest.com/zolocanada","https://www.linkedin.com/company/zolocanada","https://www.youtube.com/c/ZoloCanada","https://www.instagram.com/zolocanada","https://www.glassdoor.ca/Reviews/Zolo-Reviews-E715995.htm"]
    }
    ,{
        "@context": "http://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
              {
                    "@type": "ListItem",
                    "position": 1, "item": {
                    "@id": "https://www.zolo.ca",
                    "name": "Canada",
                    "type": "WebPage"
                } },{
                    "@type": "ListItem",
                    "position": 2, "item": {
                    "@id": "https://www.zolo.ca/ontario-real-estate",
                    "name": "Ontario",
                    "type": "WebPage"
                } },{
                    "@type": "ListItem",
                    "position": 3, "item": {
                    "@id": "https://www.zolo.ca/ottawa-real-estate",
                    "name": "Ottawa Real Estate",
                    "type": "WebPage"
                } }        ]
    }
            ,{
            "@context": "http://schema.org/",
            "@type": "DataFeed",
             "name": "Ottawa MLS&reg; Listings &amp; Real Estate for Sale",
            "description": "6340 homes for sale in Ottawa ON &ndash; See photos of new Ottawa Real Estate & MLS Listings &bull; Open Houses &ndash; Faster than MLS.ca & updated every 15 mins!",
            "dateModified": "2025-08-09T08:20:11-06:00"
        }

    ,{
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [{
        "@type": "Question",
        "name": "What is the average home price in Ottawa, ON?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "According to current Ottawa MLS&copy; statistics, the average home listing price in Ottawa, ON is $877,000. Based on Ottawa housing inventory, the average home is listed on the market for 31 days and has a 97.8% selling to listing price ratio. "
        }}, {
        "@type": "Question",
        "name": "How much does a detached house cost in Ottawa?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Based on current Ottawa MLS&copy; data, the average detached house in Ottawa, ON has a listing price of $1,033,000. In Ottawa, detached houses are on the market for 29 days on average."
        }}, {
        "@type": "Question",
        "name": "How much does a condo cost in Ottawa?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "According to current Ottawa MLS&copy; data, the average condo in Ottawa, ON has a listing price of $479,000. In Ottawa, the average price for a 2-bedroom condo is $493,000 and the average price for a 1-bedroom condo is $397,000."
        }}, {
        "@type": "Question",
        "name": "What are the most expensive neighbourhoods in Ottawa?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "In Ottawa, ON, the most expensive neighbourhoods to buy a home include Kinburn, Rothwell Heights, Experimental Farm and Mooneys Bay. Kinburn is the most expensive neighbourhood in Ottawa with an average home price of $835,000."
        }}, {
        "@type": "Question",
        "name": "What are the cheapest neighbourhoods to buy a home in Ottawa?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "The cheapest neighbourhoods to buy a home in Ottawa include Heron Gate, Carleton Square, Viscount Alexander Park and Britannia Heights. Heron Gate is the most affordable neighbourhood in Ottawa with an average home price of $248,000."
        }}, {
        "@type": "Question",
        "name": "What are nearby cities to Ottawa, ON?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "The closest cities to Ottawa, ON include Kanata, Stittsville, Russell. Out of the nearby Ottawa cities, Stittsville has the highest average home price of $959,000 and Russell has the most affordable average home price of $751,000."
        }}, {
        "@type": "Question",
        "name": "How much does a townhouse cost in Ottawa?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "The average townhouse in Ottawa has an average listing price of $687,000, which represents a -18.7% change in price from last year. There are currently 1608 townhouses for sale in Ottawa, ON."
        }}, {
        "@type": "Question",
        "name": "What's the average income of a Ottawa, ON resident?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "According to Statistics Canada Census data, the median total household income in Ottawa is $79,634 per year, which is above the national median income of $67,000 per year."
        }}, {
        "@type": "Question",
        "name": "How many people rent versus buy real estate in Ottawa?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "In Ottawa, ON, fewer people rent their homes than own at 32.7% rent versus 67.3% own. The median monthly rent price is $2,500 per month versus the median monthly mortgage of $0 per month."
        }}]
    }
    ]
</script>

</head>



<body class="home gallery logged-out  preload">



<header class="header gut xs-flex xs-full-width fill-white xs-z4 xs-relative" id="header">
    <div class="nav-top xs-relative xs-flex xs-flex-align-center xs-full-width">
        <div class="xs-flex xs-flex-align-center fill-white xs-full-width xs-z4 xs-gut-neg xs-px3 md-px0 xs-flex-grow-1">

        <a href="https://www.zolo.ca/" class="logo-zolo xs-flex-shrink-0 xs-mr2 md-mr0">
            <picture>
                <source media="(max-width: 63.975rem)" srcset="data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2036%2036%22%3E%3Cpath%20d%3D%22M30.7%205.3L18%2018l12.7%2012.7a17.8%2017.8%200%200%200%200-25.4z%22%20fill%3D%22%23fdb725%22%2F%3E%3Cpath%20d%3D%22M5.3%205.3L18%2018%2030.7%205.3a17.8%2017.8%200%200%200-25.4%200z%22%20fill%3D%22%2371bf44%22%2F%3E%3Cpath%20d%3D%22M5.3%205.3a17.8%2017.8%200%200%200%200%2025.4L18%2018z%22%20fill%3D%22%23008fd5%22%2F%3E%3Cpath%20d%3D%22M18%2018L5.3%2030.7a17.8%2017.8%200%200%200%2025.4%200z%22%20fill%3D%22%23f36f21%22%2F%3E%3Ccircle%20cx%3D%2218%22%20cy%3D%2218%22%20r%3D%2214.7%22%20fill%3D%22%23fff%22%2F%3E%3Cpath%20d%3D%22M23.1%2026.6H12.9a.9.9%200%200%201-.8-.7v-2a.6.6%200%200%201%20.2-.5l7.6-10.9h-7a.8.8%200%200%201-.8-.8v-1.5a.9.9%200%200%201%20.8-.8h10.2a.8.8%200%200%201%20.8.8v2.1a1.1%201.1%200%200%201-.2.5l-7.3%2010.8h6.7a.8.8%200%200%201%20.8.8v1.5a.8.8%200%200%201-.8.7z%22%20fill%3D%22%23282828%22%20%2F%3E%3C%2Fsvg%3E">
                <img src="data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 115 30'%3e%3crect width='6.1' height='9.9' rx='.8' fill='%23008fd5'%3e%3c/rect%3e%3crect y='12' width='6.1' height='18' rx='.8' fill='%23fdb725'%3e%3c/rect%3e%3crect x='8.2' width='6.1' height='15' rx='.8' fill='%2348b6e2'%3e%3c/rect%3e%3crect x='8.2' y='17.1' width='6.1' height='12.9' rx='.8' fill='%2371bf44'%3e%3c/rect%3e%3crect x='16.4' width='6.1' height='6.1' rx='.8' fill='%23008fd5'%3e%3c/rect%3e%3crect x='16.4' y='8.2' width='6.1' height='21.8' rx='.8' fill='%23f36f21'%3e%3c/rect%3e%3crect x='24.6' width='6.1' height='21.8' rx='.8' fill='%23008fd5'%3e%3c/rect%3e%3crect x='24.6' y='23.9' width='6.1' height='6.1' rx='.8' fill='%23fdb725'%3e%3c/rect%3e%3crect x='32.8' width='6.1' height='17.4' rx='.8' fill='%2348b6e2'%3e%3c/rect%3e%3crect x='32.8' y='19.5' width='6.1' height='10.5' rx='.8' fill='%2371bf44'%3e%3c/rect%3e%3cpath d='M101.3 25c-1.2-.6-2.2-1.6-2.8-3.1s-.9-3.8-.9-6.7.3-5.2 1-6.8c.6-1.6 1.6-2.6 2.8-3.2s2.9-.9 4.9-.9c2.1 0 3.7.3 5 .9 1.2.6 2.2 1.6 2.8 3.2s.9 3.8.9 6.8-.3 5.2-.9 6.7a5.4 5.4 0 0 1-2.8 3.1c-1.2.6-2.9.8-5 .8s-3.7-.3-5-.8zm7.7-3.5c.6-.4 1.1-1 1.3-2 .3-1 .4-2.4.4-4.4s-.1-3.5-.4-4.5-.7-1.6-1.3-2c-.6-.3-1.5-.5-2.7-.5s-2.1.2-2.7.5-1.1 1-1.4 2-.4 2.5-.4 4.5.1 3.4.4 4.4.7 1.6 1.3 2 1.5.5 2.8.5c1.2 0 2.1-.2 2.7-.5zm-13.4.6h-8.2V5.3a.94.94 0 0 0-1-1h-2.3c-.5.1-.8.5-.8 1v19.5a.94.94 0 0 0 1 1h11.3c.5 0 .9-.4 1-.9v-1.8a1.08 1.08 0 0 0-1-1zm-29 2.9c-1.2-.6-2.2-1.6-2.8-3.1s-.9-3.8-.9-6.7.3-5.2 1-6.8c.6-1.6 1.6-2.6 2.8-3.2s2.9-.9 4.9-.9c2.1 0 3.7.3 5 .9 1.2.6 2.2 1.6 2.8 3.2s.9 3.8.9 6.8-.3 5.2-.9 6.7a5.4 5.4 0 0 1-2.8 3.1c-1.2.6-2.9.8-5 .8s-3.7-.3-5-.8zm7.7-3.5c.6-.4 1.1-1 1.3-2 .3-1 .4-2.4.4-4.4s-.1-3.5-.4-4.5-.7-1.6-1.3-2c-.6-.3-1.5-.5-2.7-.5s-2.1.2-2.7.5-1.1 1-1.4 2-.4 2.5-.4 4.5.1 3.4.4 4.4.7 1.6 1.3 2 1.5.5 2.8.5c1.2 0 2.1-.2 2.7-.5zm-14.4.6h-8.4l9.1-13.6c.1-.2.2-.4.2-.6V5.1c-.1-.5-.5-.8-1-.8H47.1a.94.94 0 0 0-1 1v2c0 .5.4.9.9.9h8.9l-9.6 13.6c-.2.2-.2.4-.2.6v2.5c0 .5.4.9.9.9h12.9c.5 0 .9-.4.9-.9V23c0-.5-.4-.9-.9-.9z' fill='%23282828'%3e%3c/path%3e%3c/svg%3e" alt="Canada MLS" width="115" height="30" />
            </picture>
        </a>

                    <div class="nav-top-search min-width-0 xs-full-width xs-relative xs-flex xs-flex-align-center md-hide print-hide">
                <div class="xs-flex xs-flex-align-center xs-px2 xs-py1 truncate rounded fill-grey-bg-2 xs-full-width xs-h-40 drawer-location-toggle">
                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary xs-mr1" aria-hidden="true"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                    <div class="truncate xs-full-width">
                        Ottawa                    </div>
                </div>
                                    <button class="button button--menu xs-h-40 xs-w-40 xs-p0 xs-border-none circle xs-flex xs-flex-align-center xs-flex-justify-center xs-ml1 drawer-filters-toggle" style="margin-right:-.5rem;" aria-label="filter results">
                        <div class="xs-inline-flex xs-relative">
                            <svg viewBox="0 0 24 24" height="24" width="24" aria-hidden="true"><path d="M3 17v2h6v-2H3zM3 5v2h10V5H3zm10 16v-2h8v-2h-8v-2h-2v6h2zM7 9v2H3v2h4v2h2V9H7zm14 4v-2H11v2h10zm-6-4h2V7h4V5h-4V3h-2v6z"/></svg>
                                                    </div>
                    </button>
                            </div>

                <div class="nav-filters-location xs-hide md-block xs-p3 md-p0 xs-relative xs-border-bottom-lighter md-border-bottom-none clearfix xs-mx3 xs-full-width" style="min-width:120px; max-width:400px;">
            <form id="location_only_search" name="location_only_search">
                <div class="filter-location-input xs-full-width xs-relative">
                    <label for="sarea" class="sr-only">Search by location</label>
                    <input class="text-input xs-full-width xs-pl3 xs-pr4 pill no-focus fill-grey-bg-2 xs-border-none" type="text" placeholder="Search by location..." name="sarea" id="sarea" autocomplete="off" value="Ottawa" style="height:3rem">
                    <button class="submit-search button button--menu xs-h-40 xs-w-40 xs-p0 xs-border-none circle xs-flex xs-flex-align-center xs-flex-justify-center hover:text-blue xs-absolute xs-t05 xs-r1" type="submit" aria-label="Submit search">
                        <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current" aria-hidden="true"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                    </button>
                </div>
                                <input type="hidden" name="sarea_hidden" value="Ottawa " id="sarea_hidden">
                <input type="hidden" name="search_order" value="0" class="js-search-order">
            </form>
        </div>

        </div>

        <div id="drawer-menu" class="drawer drawer-ltr drawer-desktop drawer-menu fill-white print-hide">
            <div class="drawer-body xs-py2 gut md-p0">
                <nav class="xs-flex xs-flex-column md-flex-row">
                    <div class="xs-flex xs-flex-column md-flex-row md-flex-align-center xs-flex-order-3 md-flex-order-1">
                                                                            <hr>
                            <section class="nav-section dropdown xs-relative">

                                <button data-toggle="dropdown" class="button button--menu xs-inline-flex xs-flex-align-center xs-text-3 md-text-5 xs-text-left normal xs-full-width hover:fill-grey-bg-2">
                                    Buy
                                    <svg viewBox="0 0 24 24" class="fill-current xs-ml-auto md-ml05 pointer-events-none" height="16" width="16" style="margin-right:-.5rem" aria-hidden="true"><path d="M7.41 8.58L12 13.17l4.59-4.59L18 10l-6 6-6-6 1.41-1.42z"/></svg>
                                </button>

                                <div class="dropdown-menu-topnav dropdown-menu fill-white xs-border-none xs-p0 md-border rounded md-p1">
                                    <a href="/ottawa-real-estate" class="button button--menu xs-full-width xs-text-left hover:fill-blue-highlight hover:text-blue bold">Ottawa Real Estate</a>
                                    <a href="/ottawa-real-estate/houses" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Houses for Sale</a>
                                    <a href="/ottawa-real-estate/condos" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Condos for Sale</a>
                                    <a href="/ottawa-real-estate/townhouses" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Townhouses for Sale</a>
                                                                        <a href="/ottawa-real-estate/open-houses" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Open Houses</a>
                                                                                                                                                <a href="/index.php?s_r=3" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Recently Sold</a>
                                                                                                                                                <a href="/ottawa-real-estate/commercial" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Commercial</a>
                                                                        <hr class="xs-my1" />
                                    <a href="/sitemap/ottawa" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">New Listings</a>
                                    <a href="https://www.zolo.ca/blog/buying" class="xs-flex xs-flex-align-center xs-gap-2 button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">
                                        <div class="bold">
                                            Learn about home buying
                                        </div>
                                    </a>
                                </div>
                            </section>
                                                        <hr class="md-hide" />
                            <section class="nav-section dropdown xs-relative">
                                <button data-toggle="dropdown" class="button button--menu xs-inline-flex xs-flex-align-center xs-text-3 md-text-5 xs-text-left normal xs-full-width hover:fill-grey-bg-2">
                                    Rent
                                    <svg viewBox="0 0 24 24" class="fill-current xs-ml-auto md-ml05 pointer-events-none" height="16" width="16" style="margin-right:-.5rem" aria-hidden="true"><path d="M7.41 8.58L12 13.17l4.59-4.59L18 10l-6 6-6-6 1.41-1.42z"/></svg>
                                </button>
                                <div class="dropdown-menu-topnav dropdown-menu fill-white xs-border-none xs-p0 md-border rounded md-p1">
                                    <a href="/ottawa-real-estate/for-rent" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue bold">Ottawa Rentals</a>
                                    <a href="/ottawa-real-estate/houses-for-rent" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Houses for Rent</a>
                                    <a href="/ottawa-real-estate/apartments-for-rent" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Apartments for Rent</a>
                                    <hr class="xs-my1" />
                                    <a href="/sitemap/ottawa" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">New Listings</a>
                                    <a href="https://www.zolo.ca/blog/renting" class="xs-flex xs-flex-align-center xs-gap-2 button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">
                                        <div class="bold">
                                            Learn about home renting
                                        </div>
                                    </a>
                                </div>
                            </section>
                                                                            <hr class="md-hide" />
                        <section class="nav-section dropdown xs-relative">
                            <button data-toggle="dropdown" class="button button--menu xs-inline-flex xs-flex-align-center xs-text-3 md-text-5 xs-text-left normal xs-full-width hover:fill-grey-bg-2">
                                Sell
                                <svg viewBox="0 0 24 24" class="fill-current xs-ml-auto md-ml05 pointer-events-none" height="16" width="16" style="margin-right:-.5rem" aria-hidden="true"><path d="M7.41 8.58L12 13.17l4.59-4.59L18 10l-6 6-6-6 1.41-1.42z"/></svg>
                            </button>
                            <div class="dropdown-menu-topnav dropdown-menu fill-white xs-border-none xs-p0 md-border rounded md-p1">
                                <a href="https://www.zolo.ca/sell" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">Sell with Zolo</a>
                                <a href="https://www.zolo.ca/how-much-is-my-home-worth" class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">How Much is My Home Worth?</a>
                                <hr class="xs-my1">
                                <a href="https://www.zolo.ca/blog/selling" class="xs-flex xs-flex-align-center xs-gap-2 button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue">
                                    <div class="bold">
                                        Learn about home selling
                                    </div>
                                </a>
                            </div>
                        </section>


                        <hr class="md-hide" />
                        <a href="https://www.zolo.ca/blog/" class="button button--menu xs-text-3 md-text-5 xs-text-left normal xs-full-width hover:fill-grey-bg-2">Blog</a>
                        <hr class="md-hide" />
                        <a href="https://www.zolo.ca/jobs" class="button button--menu xs-text-3 md-text-5 xs-text-left normal xs-full-width hover:fill-grey-bg-2">Jobs</a>
                    </div>
                    <div class="xs-flex xs-flex-column md-flex-row xs-flex-order-2 md-flex-order-3 md-ml05">
                        <section class="nav-section dropdown xs-relative">
                            <button data-toggle="dropdown" class="button button--menu xs-h-40 xs-w-40 xs-p0 xs-border-none hover:fill-grey-bg-2 circle xs-hide md-flex md-flex-align-center md-flex-justify-center" aria-label="Saved items">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current pointer-events-none" aria-hidden="true"><path d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/></svg>
                            </button>
                            <button data-toggle="dropdown" class="button button--menu xs-inline-flex xs-flex-align-center xs-text-3 md-text-5 xs-text-left normal xs-full-width hover:fill-grey-bg-2 md-hide">
                                Saved Items
                                <svg viewBox="0 0 24 24" class="fill-current xs-ml-auto md-ml05 pointer-events-none" height="16" width="16" style="margin-right:-.5rem" aria-hidden="true"><path d="M7.41 8.58L12 13.17l4.59-4.59L18 10l-6 6-6-6 1.41-1.42z"/></svg>
                            </button>
                            <div class="dropdown-menu-topnav dropdown-menu fill-white xs-border-none xs-p0 md-border rounded md-p1 xs-r0 xs-l-auto">
                                <a class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue xs-flex xs-flex-align-center" href="https://www.zolo.ca/account/saved-searches" rel="nofollow">Saved Searches</a>
                                <a class="button button--menu xs-full-width xs-text-left normal hover:fill-blue-highlight hover:text-blue xs-flex xs-flex-align-center" href="https://www.zolo.ca/account/favorites" rel="nofollow">Saved Homes</a>
                            </div>
                        </section>
                        <a href="https://www.zolo.ca/account/notifications" class="button button--menu xs-h-40 xs-w-40 xs-p0 xs-border-none hover:fill-grey-bg-2 circle xs-hide md-flex md-flex-align-center md-flex-justify-center xs-flex-shrink-0" rel="nofollow" aria-label="notifications">
                            <div class="xs-inline-flex xs-relative">
                                <svg viewBox="0 0 24 24" height="24" width="24" aria-hidden="true"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/></svg>
                                                            </div>
                        </a>
                    </div>
                                            <div class="xs-flex xs-flex-column md-flex-row xs-flex-order-1 md-flex-order-3 md-ml1">
                                                                <button class="button button--large button--white-black xs-full-width md-hide signup-btn"><span class="text-blue bold">Register</span> or <span class="text-blue bold">Sign In</span></button>
                                    <button class="button button--menu xs-text-3 md-text-5 xs-text-left normal xs-full-width fill-grey-bg-2 hover:fill-grey-bg-2 xs-hide md-block signup-btn">Sign In</button>
                                                        <hr class="md-hide" />
                        </div>
                                    </nav>
            </div>
        </div>
        <div class="backdrop"></div>
    </div>
</header>

<section class="drawer drawer-bottom-up drawer-desktop drawer-top drawer-filters section-search clearfix xs-full-width fill-white md-py2 md-relative md-z3 md-shadow-bottom" id="drawer-filters">
    <!--
    <header class="header drawer-header xs-flex xs-flex-align-center gut md-hide xs-shadow-bottom xs-z4 xs-relative">
        <button class="drawer-filters-cancel drawer-filters-close button button--menu text-secondary xs-text-4 hover:fill-grey-bg-2" style="margin-left: -1rem;" data-dismiss="modal" aria-hidden="true">
            cancel
        </button>
        <div class="xs-relative xs-ml-auto">
            <a href="#" class="drawer-filters-apply xs-relative button fill-primary hover:fill-secondary js-button-filter" onclick="$('.drawer-header-value-refresh').addClass('loading').show(); window.location = ZOLO_SEARCH_UI.searchURL;" style="margin-right:-1rem;min-width:11rem">
                See <span id="listings_results">6340</span> Homes

                <div class="drawer-header-value-refresh rounded xs-absolute xs-t0 xs-r0 xs-b0 xs-l0 xs-text-center xs-pt1 hide" style="background-color: rgba(0,0,0,0.25);">
                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-white fill-primary circle" aria-hidden="true"><path d="M17.65 6.35A7.99 7.99 0 1019.73 14h-2.08a6 6 0 11-1.43-6.22L13 11h7V4l-2.35 2.35z"/></svg>
                </div>
            </a>
        </div>
    </header>
    -->

    <div class="header drawer-header xs-flex xs-flex-align-center xs-flex-justify-space-between xs-px1 md-hide xs-shadow-bottom xs-z4 xs-relative">
        <button class="drawer-filters-cancel drawer-filters-close button button--menu text-secondary xs-text-4 hover:fill-grey-bg-2" data-dismiss="modal" aria-hidden="true">
            Cancel
        </button>
        <div class="drawer-header-value xs-line-height-60 xs-relative">
            <span id="listings_results">6340</span> Results
        </div>
        <a href="#" class="drawer-filters-apply xs-relative button fill-primary hover:fill-secondary js-button-filter" onclick="$('.drawer-header-value-refresh').addClass('loading').show(); window.location = ZOLO_SEARCH_UI.searchURL;">Apply</a>
        <div class="drawer-header-value-refresh xs-absolute xs-t0 xs-r0 xs-b0 xs-l0 xs-text-center hide" style="background-color: rgba(255,255,255,0.5);">
            <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-blue xs-mt2" aria-hidden="true"><path d="M17.65 6.35A7.99 7.99 0 1019.73 14h-2.08a6 6 0 11-1.43-6.22L13 11h7V4l-2.35 2.35z"/></svg>
        </div>
    </div>

    <div class="drawer-body gut xs-z3">
        <form id="home_search_top" class="nav-filters-form clearfix md-flex md-flex-align-center" name="home_search_top" action="https://www.zolo.ca/index.php">
            <input type="hidden" name="filter" value="1">
            <input type="hidden" name="sarea" value="Ottawa">
            <input type="hidden" name="sarea_hidden" value="Ottawa" id="sarea_hidden">
            <input type="hidden" name="search_order" value="0" id="search_order">
            <input type="hidden" name="s_r" value="1" id="s_r">
                            <ul class="nav-filters-menu xs-relative md-flex md-flex-align-center md-flex-grow-1 list-unstyled">
                    <li class="filter-status dropdown md-mr1 ">
                        <a href="#" class="button button--small button--white-black bold xs-hide md-inline-block js-button-filter" id="status_desc">For Sale</a>

                        <div class="dropdown-menu xs-full-width" style="min-width:120px;">
                            <div class="filter-title xs-text-3 bold xs-mb2 md-hide">Status</div>
                            <div class="filter-status-values">
                                <ul class="dd-list list-unstyled xs-flex xs-flex-wrap md-flex-column">
                                    <li class="dd-list-item">
                                        <button type="button" data-value="1" class="button button--dd selected js-search-filter js-search-filter-button">For Sale</button>
                                    </li>
                                                                        <li class="dd-list-item">
                                        <button type="button" data-value="2" class="button button--dd   js-search-filter js-search-filter-button">For Rent</button>
                                    </li>
                                                                                                            <li class="dd-list-item">
                                        <button type="button" data-value="3" class="button button--dd   " onclick="window.location.href='/index.php?s_r=3'">Sold</button>
                                    </li>
                                                                    </ul>
                            </div>
                        </div>
                    </li>
                    <li class="filter-price md-mr1 dropdown dropdown-keep-open" id="price-filter">
                        <a href="#" class="button button--small button--white-black bold xs-hide md-inline-block js-button-filter" id="price_desc">Any Price</a>

                        <div class="dropdown-menu" style="min-width: 260px;">
                            <div class="filter-title xs-text-3 bold xs-mb2 md-hide">Price</div>
                            <div class="filter-range xs-flex xs-flex-align-center">
                                <div class="range-min xs-flex-grow-1 xs-col-6 xs-pr05">
                                    <label for="price-min" class="sr-only">Minimum price</label>
                                    <input class="text-input rounded js-search-filter js-search-min-price" maxlength="11" size="10" name="min_price" id="price-min" type="tel" pattern="[0-9]*" data-validate="true" data-required="false" placeholder="Min" autocomplete="off" value="">
                                </div>
                                <div class="range-max xs-flex-grow-1 xs-col-6 xs-pl05">
                                    <label for="price-max" class="sr-only">Maximum price</label>
                                    <input class="text-input rounded js-search-filter js-search-max-price" maxlength="11" size="11" name="max_price" id="price-max" type="tel" pattern="[0-9]*" data-validate="true" data-required="false" placeholder="Max" autocomplete="off" value="">
                                </div>
                            </div>

                            <div class="filter-price-values xs-mt1 xs-hide md-flex">
                                <ul class="filter-price-min list-unstyled xs-col-6 xs-pr05 xs-mr-auto">
                                    <li class="dd-list-item">
                                        <a data-value="0" class="button button--dd selected js-search-filter">0</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="200000" class="button button--dd js-search-filter">$200,000+</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="300000" class="button button--dd js-search-filter">$300,000+</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="400000" class="button button--dd js-search-filter">$400,000+</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="600000" class="button button--dd js-search-filter">$600,000+</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="800000" class="button button--dd js-search-filter">$800,000+</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="1000000" class="button button--dd js-search-filter">$1,000,000+</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="1500000" class="button button--dd js-search-filter">$1,500,000+</a>
                                    </li>
                                </ul>
                                <ul class="filter-price-max list-unstyled xs-col-6 xs-pl05 xs-ml-auto">
                                    <li class="dd-list-item">
                                        <a data-value="100000" class="button button--dd xs-text-right js-search-filter">$100,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="150000" class="button button--dd xs-text-right js-search-filter">$150,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="200000" class="button button--dd xs-text-right js-search-filter">$200,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="250000" class="button button--dd xs-text-right js-search-filter">$250,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="300000" class="button button--dd xs-text-right js-search-filter">$300,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="400000" class="button button--dd xs-text-right js-search-filter">$400,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="500000" class="button button--dd xs-text-right js-search-filter">$500,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="600000" class="button button--dd xs-text-right js-search-filter">$600,000</a>
                                    </li>
                                    <li class="dd-list-item">
                                        <a data-value="0" class="button button--dd xs-text-right selected js-search-filter">Any Price</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </li>
                                        <li class="filter-bed md-mr1 dropdown">
                        <a href="#" class="button button--small button--white-black bold xs-hide md-inline-block js-button-filter" id="min_bed_desc">0+ Bed</a>
                        <div class="dropdown-menu xs-full-width">
                            <div class="filter-title xs-text-3 bold xs-mb2 md-hide">Beds</div>
                            <div class="filter-bed-values">
                                <ul class="dd-list list-unstyled xs-flex xs-flex-wrap md-flex-column">
                                    <li class="dd-list-item"><a class="button button--dd selected js-search-filter" data-value="0">0+</a></li><li class="dd-list-item"><a data-value="1" class="button button--dd js-search-filter">1+</a></li><li class="dd-list-item"><a data-value="2" class="button button--dd js-search-filter">2+</a></li><li class="dd-list-item"><a data-value="3" class="button button--dd js-search-filter">3+</a></li><li class="dd-list-item"><a data-value="4" class="button button--dd js-search-filter">4+</a></li><li class="dd-list-item"><a data-value="5" class="button button--dd js-search-filter">5+</a></li>                                </ul>
                            </div>
                        </div>
                    </li>
                    <input type="hidden" id="min_beds" name="min_beds" value="0">
                    <li class="filter-property-type md-mr1 dropdown dropdown-keep-open">
                        <a href="#" class="button button--small button--white-black bold xs-hide md-inline-block js-button-filter" id="ptype_desc">Home Type</a>
                        <div class="dropdown-menu">
                            <div class="filter-title xs-text-3 bold xs-mb2 md-hide">Home Type</div>
                            <ul class="dd-list list-unstyled xs-flex md-flex-column" style="min-width: 160px;">
                                <li class="dd-list-item md-pb0">
                                    <input name="ptype_house" tabindex="-1" id="property-type-house" type="checkbox" class="checkbox js-search-filter js-property-type" value="1" >
                                    <label for="property-type-house" class="xs-p1 xs-text-center md-text-left xs-border md-border-none rounded"><span class="">House</span></label>
                                </li>
                                <li class="dd-list-item md-pb0 xs-text-center">
                                    <input name="ptype_condo" tabindex="-1" id="property-type-condo" type="checkbox" class="checkbox js-search-filter js-property-type" value="1" >
                                    <label for="property-type-condo" class="xs-p1 xs-text-center md-text-left xs-border md-border-none rounded"><span class="">Condo</span></label>
                                </li>
                                <li class="dd-list-item md-pb0 xs-text-center">
                                    <input name="ptype_townhouse" tabindex="-1" id="property-type-townhouse" type="checkbox" class="checkbox js-search-filter js-property-type" value="1" >
                                    <label for="property-type-townhouse" class="xs-p1 xs-text-center md-text-left xs-border md-border-none rounded"><span class="">Townhouse</span></label>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="filter-more dropdown md-mr1 xs-pt3 md-pt0 dropdown-keep-open">
                        <a href="#" class="button button--small button--white-black bold xs-hide md-inline-block js-button-filter" id="more_search_desc">More </a>
                        <div class="dropdown-menu xs-r0 xs-l-auto">
                            <div class="filter-title xs-text-3 bold xs-mb2 md-hide">
                                More Filters
                            </div>
                            <ul class="list-unstyled md-mx1 md-mt1" style="min-width: 272px;">
                                <li class="filter-bath xs-mb2 xs-flex xs-flex-align-center">
                                    <label for="min_baths" class="filter-label xs-flex-shrink-0 xs-pr1 xs-py1">Baths</label>
                                    <div class="filter-input xs-full-width">
                                        <select class="select rounded js-search-filter js-search-filter-apply" name="min_baths" id="min_baths">
                                            <option value="0" selected>0+</option><option value="1">1+</option><option value="2">2+</option><option value="3">3+</option><option value="4">4+</option>                                        </select>
                                    </div>
                                </li>
                                <li class="filter-size xs-mb2 xs-flex xs-flex-align-center">
                                    <label for="min_sqft" class="filter-label xs-flex-shrink-0 xs-pr1 xs-py1">Square Feet</label>
                                    <div class="filter-input xs-full-width">
                                        <select class="select rounded js-search-filter js-search-filter-apply" name="min_sqft" id="min_sqft">
                                            <option value="0">Any</option><option value="500">500+ sq ft</option><option value="750">750+ sq ft</option><option value="1000">1,000+ sq ft</option><option value="1250">1,250+ sq ft</option><option value="1500">1,500+ sq ft</option><option value="1750">1,750+ sq ft</option><option value="2000">2,000+ sq ft</option><option value="2250">2,250+ sq ft</option><option value="2500">2,500+ sq ft</option><option value="2750">2,750+ sq ft</option><option value="3000">3,000+ sq ft</option><option value="3250">3,250+ sq ft</option><option value="3500">3,500+ sq ft</option>                                        </select>
                                    </div>
                                </li>
                                <li class="filter-days xs-mb2 xs-flex xs-flex-align-center">
                                    <label for="days_on_zolo" class="filter-label xs-flex-shrink-0 xs-pr1 xs-py1">Days on Zolo</label>
                                    <div class="filter-input xs-full-width">
                                        <select class="select rounded js-search-filter js-search-filter-apply" name="days_on_zolo" id="days_on_zolo">
                                            <option value="0" selected>Any</option>
                                            <option value="1" >1 day</option>
                                            <option value="7" >7 days</option>
                                            <option value="14" >14 days</option>
                                            <option value="30">30 days</option>
                                            <option value="90" >90 days</option>
                                        </select>
                                    </div>
                                </li>
                                <li class="filter-photos xs-mb2 xs-flex xs-flex-align-center">
                                    <label for="has_photos" class="filter-label xs-flex-shrink-0 xs-pr1 xs-py1">Has Photos?</label>
                                    <div class="filter-input xs-full-width">
                                        <select class="select rounded js-search-filter js-search-filter-apply" name="has_photos" id="has_photos">
                                            <option value="0" >0+</option>
                                            <option value="1" >1+</option>
                                            <option value="2" >2+</option>
                                            <option value="5" >5+</option>
                                            <option value="10" >10+</option>
                                        </select>
                                    </div>
                                </li>
																	<li class="filter-open-house xs-mb2 xs-flex xs-flex-align-center">
											<div class="filter-label xs-flex-shrink-0 xs-pr1 xs-py1">Open Houses</div>

										<div class="filter-input xs-full-width">
											<div class="xs-inline-block md-block xs-width-auto">
												<input class="checkbox js-search-filter js-search-filter-apply js-open-house-search" id="openhouse_search" name="openhouse_search" tabindex="-1" type="checkbox" value="1" >
												<label for="openhouse_search" aria-label="Must have an open house" class="xs-py1"></label>
											</div>
										</div>
									</li>
								                                <li class="filter-keywords xs-mb2 xs-flex xs-flex-align-center">
                                    <label for="attribute-terms" class="filter-label xs-flex-shrink-0 xs-pr1 xs-py1">Keywords</label>
                                    <div class="filter-input xs-full-width">
                                        <input class="text-input rounded xs-full-width js-search-filter js-search-filter-apply" id="attribute-terms" placeholder="e.g. pool, suite" name="attribute_terms" value="" />
                                    </div>
                                </li>
                                <li class="filter-apply xs-mb1 xs-hide md-flex">
                                    <div class="filter-label xs-flex-shrink-0 xs-pr1 xs-py1">&nbsp;</div>
                                    <div class="filter-input xs-full-width">
                                        <button class="button xs-full-width" type="submit">Apply</button>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="filter-save-search xs-mr1 xs-hide md-inline-block xs-relative dropdown">
                        <button class="button button--small button--white-black bold xs-inline-flex xs-flex-align-center" data-toggle="modal" data-target="#savesearchModal">
                            Save Search
                            <svg viewBox="0 0 24 24" height="14" width="14" class="xs-line-height-1 xs-ml05 fill-current text-blue xs-flex-shrink-0"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09A5.99 5.99 0 0116.5 3C19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>
                        </button>
                    </li>
                                            <li class="md-ml-auto xs-hide md-inline-block">

                                <button class="button button--small button--white-black xs-mr1 xs-inline-flex xs-flex-align-center" data-toggle="modal" data-target="#sortModal"><span class="bold xs-mr05">Sort:</span><span>Recommended</span></button>
                                <a href="https://www.zolo.ca/map-search" class="button button--small button--white-black bold xs-inline-flex xs-flex-align-center" onclick="resetMapSession();">
                                    Map Search
                                    <svg viewBox="0 0 24 24" height="14" width="14" class="xs-ml05 fill-current text-blue xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                </a>
                                                    </li>
                                    </ul>
                    </form>
    </div>
</section>

<main id="listing_container" class="transition-after-load gut">


<section class="headings xs-pt4 xs-mb3">
    <h1 class="xs-inline-block heavy xs-text-2 sm-text-1 xs-mr2">Ottawa Real Estate</h1>



            <p class="xs-hide sm-block xs-mt1 xs-mb2">Refine your Ottawa real estate search by price, bedroom, or type (house, townhouse, or condo). View up-to-date MLS&reg; listings in Ottawa.</p>

    <div class="headings-menu xs-flex xs-flex-align-center xs-flex-wrap xs-text-5 xs-mt1">

                                    <div class="xs-mr-auto">
                                                            <a class="xs-inline-flex button--white-black pill xs-px2 xs-py1 xs-line-height-1 xs-text-5 xs-mr05 xs-mt1" href="https://www.zolo.ca/ottawa-real-estate/neighbourhoods">Neighbourhoods</a>
                                                                                <a class="xs-inline-flex xs-flex-align-center button--white-black pill xs-px2 xs-py1 xs-line-height-1 xs-text-5 xs-mr05 xs-mt1" href="index.php?s_r=3">
                    <span class="xs-hide sm-inline xs-mr05">Recently</span> Sold
                    <svg viewBox="0 0 24 24" height="12" width="12" class="fill-current text-red xs-ml05" style="margin-right:-.25rem" aria-hidden="true"><circle cx="12" cy="12" r="6"></circle></svg>
                    </a>
                                    </div>

                    <h3 class="xs-text-5 bold xs-mt1 xs-hide sm-block">6340 homes for sale in Ottawa, ON.</h3>

    </div>

</section>

    <nav style="will-change: transform;" class="nav-actions xs-z3 fill-white xs-flex xs-flex-align-center xs-flex-justify-space-between xs-fixed xs-b0 xs-l0 xs-r0 xs-px1 text-5 xs-shadow-top md-hide print-hide">
                <a href="https://www.zolo.ca/map-search" class="button button--menu xs-border-none text-6 xs-line-height-1 xs-py1" onclick="resetMapSession();">
            <svg viewBox="0 0 24 24" height="24" width="24"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zM7 9a5 5 0 0110 0c0 2.88-2.88 7.19-5 9.88C9.92 16.21 7 11.85 7 9z"/><circle cx="12" cy="9" r="2.5"/></svg>
            <div>Map</div>
        </a>
        <button class="button button--menu xs-border-none text-6 xs-line-height-1 xs-py1" data-toggle="modal" data-target="#sortModal">
            <svg viewBox="0 0 24 24" height="24" width="24" aria-hidden="true"><path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z"/></svg>
            <div>Sort</div>
        </button>
        <button class="button button--menu xs-border-none text-6 xs-line-height-1 xs-py1" data-toggle="modal" data-target="#savesearchModal">
            <svg viewBox="0 0 24 24" height="24" width="24" aria-hidden="true"><path d="M14.7 14.5l-.3-.3c.4-.5.8-1.1 1-1.7l-1.6-1.6c-.4 2-2.2 3.6-4.4 3.6C7 14.5 5 12.5 5 10s2-4.5 4.5-4.5h.5v-2h-.5C5.9 3.5 3 6.4 3 10s2.9 6.5 6.5 6.5c1.6 0 3.1-.6 4.2-1.6l.3.3v.8l5 5 1.5-1.5-5-5h-.8zM17 11.2l.7-.7C20.3 8.2 22 6.6 22 4.7 22 3.2 20.8 2 19.3 2c-.9 0-1.7.4-2.3 1-.5-.6-1.4-1-2.3-1C13.2 2 12 3.2 12 4.7c0 1.9 1.7 3.4 4.3 5.8l.7.7z"/></svg>
            <div>Save</div>
        </button>
        <a href="https://www.zolo.ca/account/notifications" class="button button--menu xs-border-none text-6 xs-line-height-1 xs-py1 button--alert xs-relative" rel="nofollow">
            <div class="xs-inline-flex xs-relative">
                <svg viewBox="0 0 24 24" height="24" width="24" aria-hidden="true"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/></svg>
                            </div>
            <div>Alerts</div>
        </a>
                <button class="button button--menu xs-border-none text-6 xs-line-height-1 xs-py1 drawer-menu-toggle">
            <svg viewBox="0 0 24 24" height="24" width="24" aria-hidden="true"><path d="M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/></svg>
            <div>Menu</div>
        </button>
    </nav>

<section id="gallery" class="listings-container xs-mt3 md-mt0">
    <div class="listings-wrapper xs-flex xs-flex-wrap xs-grid sm-grid-cols-2 lg-grid-cols-3 xl-grid-cols-4 xs-gap-2 sm-gap-3 xs-mx2- sm-mx0">

            <article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/64-songbird-private" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">64 Songbird Private</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Westcliffe Estates</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.310562"><meta itemprop="longitude" content="-75.82711"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">Video Tour</div><script type="application/ld+json">{
                            "@context": "http://schema.org",
                            "@type": "Event",
                            "name": "Video Tour: 64 Songbird Private",
                            "startDate": "2025-08-09",
                            "url": "https://www.zolo.ca/ottawa-real-estate/64-songbird-private",
                            "eventAttendanceMode": "http://schema.org/OnlineEventAttendanceMode",
                            "eventStatus": "http://schema.org/EventScheduled",
                            "location": [
                                {
                                    "@type": "VirtualLocation",
                                    "url": "https://www.zolo.ca/ottawa-real-estate/64-songbird-private"
                                }
                            ]
                        }</script><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">3 minutes</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="542000">542,000</span></li><li class="xs-inline xs-mr1">2 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A9130659" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; X12334971</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE TEAM REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/64-songbird-private" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            fetchpriority="high"
            sizes="(min-width:75rem) calc(33.333vw - 6rem / 3),(min-width:48rem) calc(50vw - 4.5rem / 2),100vw"
            srcset="
            https://photos.zolo.ca/64-songbird-private-ottawa-X12334971-1-p480.jpg?2025-08-09+08%3A18%3A06 480w,
            https://photos.zolo.ca/64-songbird-private-ottawa-X12334971-1-p768.jpg?2025-08-09+08%3A18%3A06 768w,
            https://photos.zolo.ca/64-songbird-private-ottawa-X12334971-1-p.jpg?2025-08-09+08%3A18%3A06 1200w"
            src="https://photos.zolo.ca/64-songbird-private-ottawa-X12334971-1-p480.jpg?2025-08-09+08%3A18%3A06"
            width="480"
            height="320"
            alt="Townhouse for sale at 64 Songbird Pt Ottawa Ontario - MLS: X12334971"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-modal-listing"
            id="18069966"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/391-leavoy-lane" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">391 Leavoy Lane</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Horton Twp</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.510002"><meta itemprop="longitude" content="-76.52092"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">Video Tour</div><script type="application/ld+json">{
                            "@context": "http://schema.org",
                            "@type": "Event",
                            "name": "Video Tour: 391 Leavoy Lane",
                            "startDate": "2025-08-09",
                            "url": "https://www.zolo.ca/ottawa-real-estate/391-leavoy-lane",
                            "eventAttendanceMode": "http://schema.org/OnlineEventAttendanceMode",
                            "eventStatus": "http://schema.org/EventScheduled",
                            "location": [
                                {
                                    "@type": "VirtualLocation",
                                    "url": "https://www.zolo.ca/ottawa-real-estate/391-leavoy-lane"
                                }
                            ]
                        }</script><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">18 minutes</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="574900">574,900</span></li><li class="xs-inline xs-mr1">2 bed</li><li class="xs-inline xs-mr1">1 bath</li><li class="xs-inline xs-mr1">700-1100 sqft</li></ul>
        <button data-favorite="A22439705" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX ABSOLUTE REALTY INC.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/391-leavoy-lane" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/391-leavoy-lane-ottawa-*********-1-p480.jpg?2025-08-09+08%3A04%3A06 480w,
            https://photos.zolo.ca/391-leavoy-lane-ottawa-*********-1-p768.jpg?2025-08-09+08%3A04%3A06 768w,
            https://photos.zolo.ca/391-leavoy-lane-ottawa-*********-1-p.jpg?2025-08-09+08%3A04%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/391-leavoy-lane-ottawa-*********-1-p480.jpg?2025-08-09+08%3A04%3A06"
            src="https://photos.zolo.ca/391-leavoy-lane-ottawa-*********-1-p480.jpg?2025-08-09+08%3A04%3A06"
            width="480"
            height="320"
            alt="House for sale at 391 Leavoy Ln Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069939"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/203-catherine-street/2002" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">2002-203 Catherine Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Ottawa Centre</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.410194"><meta itemprop="longitude" content="-75.6912"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Condo</title><path d="M12 7v-4h-6v18h12v-14h-6zM10 19h-2v-2h2v2zM10 15h-2v-2h2v2zM10 11h-2v-2h2v2zM10 7.2h-2v-2h2v2zM16 19h-2v-2h2v2zM16 15h-2v-2h2v2zM16 11h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">Video Tour</div><script type="application/ld+json">{
                            "@context": "http://schema.org",
                            "@type": "Event",
                            "name": "Video Tour:  2002 - 203 Catherine Street",
                            "startDate": "2025-08-09",
                            "url": "https://www.zolo.ca/ottawa-real-estate/203-catherine-street/2002",
                            "eventAttendanceMode": "http://schema.org/OnlineEventAttendanceMode",
                            "eventStatus": "http://schema.org/EventScheduled",
                            "location": [
                                {
                                    "@type": "VirtualLocation",
                                    "url": "https://www.zolo.ca/ottawa-real-estate/203-catherine-street/2002"
                                }
                            ]
                        }</script><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">23 minutes</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="1065000">1,065,000</span></li><li class="xs-inline xs-mr1">2 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1200-1399 sqft</li><li class="xs-inline">6-10 Years Old</li></ul>
        <button data-favorite="A22439693" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RIGHT AT HOME REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/203-catherine-street/2002" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/2002-203-catherine-street-ottawa-*********-1-p480.jpg?2025-08-09+07%3A58%3A07 480w,
            https://photos.zolo.ca/2002-203-catherine-street-ottawa-*********-1-p768.jpg?2025-08-09+07%3A58%3A07 768w,
            https://photos.zolo.ca/2002-203-catherine-street-ottawa-*********-1-p.jpg?2025-08-09+07%3A58%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/2002-203-catherine-street-ottawa-*********-1-p480.jpg?2025-08-09+07%3A58%3A07"
            src="https://photos.zolo.ca/2002-203-catherine-street-ottawa-*********-1-p480.jpg?2025-08-09+07%3A58%3A07"
            width="480"
            height="320"
            alt="Condo for sale at 203 Catherine St Unit 2002 Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069929"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/145-vaughan-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">145 Vaughan Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Almonte</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.21476"><meta itemprop="longitude" content="-76.195015"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">1 hour</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="789900">789,900</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li><li class="xs-inline">16-30 Years Old</li></ul>
        <button data-favorite="A21711113" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>CENTURY 21 SYNERGY REALTY INC.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/145-vaughan-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/145-vaughan-street-ottawa-*********-1-p480.jpg?2025-08-09+07%3A16%3A06 480w,
            https://photos.zolo.ca/145-vaughan-street-ottawa-*********-1-p768.jpg?2025-08-09+07%3A16%3A06 768w,
            https://photos.zolo.ca/145-vaughan-street-ottawa-*********-1-p.jpg?2025-08-09+07%3A16%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/145-vaughan-street-ottawa-*********-1-p480.jpg?2025-08-09+07%3A16%3A06"
            src="https://photos.zolo.ca/145-vaughan-street-ottawa-*********-1-p480.jpg?2025-08-09+07%3A16%3A06"
            width="480"
            height="320"
            alt="House for sale at 145 Vaughan St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069891"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new vow"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/245-kent-street/1505" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress"><span class="priv">1505-245</span> Kent Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Ottawa Centre</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.416325"><meta itemprop="longitude" content="-75.699547"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Condo</title><path d="M12 7v-4h-6v18h12v-14h-6zM10 19h-2v-2h2v2zM10 15h-2v-2h2v2zM10 11h-2v-2h2v2zM10 7.2h-2v-2h2v2zM16 19h-2v-2h2v2zM16 15h-2v-2h2v2zM16 11h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">1 hour</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer">$XXX,XXX</li><li class="xs-inline xs-mr1">&mdash; bed</li><li class="xs-inline xs-mr1">&mdash; bath</li><li class="xs-inline xs-mr1">&mdash; sqft</li><li class="xs-inline">&mdash; Years Old</li></ul><div class="fill-white xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 xs-absolute xs-t1 xs-r1 xs-z1 pointer hover:text-red" data-target="#signupModaltop" data-toggle="modal">
                    <svg height="24" viewBox="0 0 24 24" width="24" aria-hidden="true">
                        <path d="M18 8h-1V6A5 5 0 007 6v2H6a2 2 0 00-2 2v10c0 1.1.9 2 2 2h12a2 2 0 002-2V10a2 2 0 00-2-2zM9 6a3 3 0 116 0v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"></path>
                    </svg>
                </div><div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; <span class="priv">*********</span></span></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><div class="cover-reg-tile fill-secondary aspect-content xs-z2 pointer" data-target="#signupModaltop" data-toggle="modal">
                    <div class="xs-flex xs-flex-align-center xs-flex-justify-center xs-full-height">
                        <div class="text-white xs-text-center xs-full-width xs-px2" style="margin-top: -1rem;">
                            <div class="xs-mb05 bold">
                                <div class="xs-inline-block">
                                    <span class="decoration-underline">Join or Sign In</span> &bull;
                                </div>
                                <div class="xs-inline-block">See photos &amp; sold data</div>
                            </div>
                            <div class="xs-text-5">Real estate boards require a verified account</div>
                        </div>
                    </div>
                </div><a href="https://www.zolo.ca/ottawa-real-estate/245-kent-street/1505" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/1505-245-kent-street-ottawa-*********-1-p480.jpg?2025-08-09+06%3A46%3A07 480w,
            https://photos.zolo.ca/1505-245-kent-street-ottawa-*********-1-p768.jpg?2025-08-09+06%3A46%3A07 768w,
            https://photos.zolo.ca/1505-245-kent-street-ottawa-*********-1-p.jpg?2025-08-09+06%3A46%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/1505-245-kent-street-ottawa-*********-1-p480.jpg?2025-08-09+06%3A46%3A07"
            src="https://photos.zolo.ca/1505-245-kent-street-ottawa-*********-1-p480.jpg?2025-08-09+06%3A46%3A07"
            width="480"
            height="320"
            alt="Condo for sale at 245 Kent St Unit 1505 Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069880"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/87-esban-drive" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">87 Esban Drive</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Blossom Park/Kemp Park/Findlay Creek</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.30584"><meta itemprop="longitude" content="-75.597198"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">1 hour</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="1059900">1,059,900</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">2500-3000 sqft</li><li class="xs-inline">New</li></ul>
        <button data-favorite="A21301693" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE PERFORMANCE REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/87-esban-drive" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/87-esban-drive-ottawa-*********-1-p480.jpg?2025-08-09+06%3A33%3A06 480w,
            https://photos.zolo.ca/87-esban-drive-ottawa-*********-1-p768.jpg?2025-08-09+06%3A33%3A06 768w,
            https://photos.zolo.ca/87-esban-drive-ottawa-*********-1-p.jpg?2025-08-09+06%3A33%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/87-esban-drive-ottawa-*********-1-p480.jpg?2025-08-09+06%3A33%3A06"
            src="https://photos.zolo.ca/87-esban-drive-ottawa-*********-1-p480.jpg?2025-08-09+06%3A33%3A06"
            width="480"
            height="320"
            alt="House for sale at 87 Esban Dr Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069875"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/218-keyrock-drive" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">218 Keyrock Drive</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Kanata - Kanata Lakes/Heritage Hills</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.319912"><meta itemprop="longitude" content="-75.932289"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">1 hour</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="899000">899,000</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li><li class="xs-inline">6-15 Years Old</li></ul>
        <button data-favorite="A9123190" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RIGHT AT HOME REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/218-keyrock-drive" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/218-keyrock-drive-ottawa-*********-1-p480.jpg?2025-08-09+06%3A30%3A07 480w,
            https://photos.zolo.ca/218-keyrock-drive-ottawa-*********-1-p768.jpg?2025-08-09+06%3A30%3A07 768w,
            https://photos.zolo.ca/218-keyrock-drive-ottawa-*********-1-p.jpg?2025-08-09+06%3A30%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/218-keyrock-drive-ottawa-*********-1-p480.jpg?2025-08-09+06%3A30%3A07"
            src="https://photos.zolo.ca/218-keyrock-drive-ottawa-*********-1-p480.jpg?2025-08-09+06%3A30%3A07"
            width="480"
            height="320"
            alt="House for sale at 218 Keyrock Dr Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069872"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/313-silbrass-private" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">313 Silbrass Private</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Barrhaven - Stonebridge</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.254448"><meta itemprop="longitude" content="-75.718597"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">2 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="599000">599,000</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A14620484" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX ABSOLUTE REALTY INC.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/313-silbrass-private" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/313-silbrass-private-ottawa-*********-1-p480.jpg?2025-08-09+05%3A58%3A06 480w,
            https://photos.zolo.ca/313-silbrass-private-ottawa-*********-1-p768.jpg?2025-08-09+05%3A58%3A06 768w,
            https://photos.zolo.ca/313-silbrass-private-ottawa-*********-1-p.jpg?2025-08-09+05%3A58%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/313-silbrass-private-ottawa-*********-1-p480.jpg?2025-08-09+05%3A58%3A06"
            src="https://photos.zolo.ca/313-silbrass-private-ottawa-*********-1-p480.jpg?2025-08-09+05%3A58%3A06"
            width="480"
            height="320"
            alt="Townhouse for sale at 313 Silbrass Pt Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069862"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/91-heirloom-street-east" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">91 Heirloom Street E</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Riverside South/Gloucester Glen</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.256706"><meta itemprop="longitude" content="-75.695518"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">7 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="869999">869,999</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li><li class="xs-inline">0-5 Years Old</li></ul>
        <button data-favorite="A22438650" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE TEAM REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/91-heirloom-street-east" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/91-heirloom-street-east-ottawa-*********-1-p480.jpg?2025-08-09+01%3A32%3A06 480w,
            https://photos.zolo.ca/91-heirloom-street-east-ottawa-*********-1-p768.jpg?2025-08-09+01%3A32%3A06 768w,
            https://photos.zolo.ca/91-heirloom-street-east-ottawa-*********-1-p.jpg?2025-08-09+01%3A32%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/91-heirloom-street-east-ottawa-*********-1-p480.jpg?2025-08-09+01%3A32%3A06"
            src="https://photos.zolo.ca/91-heirloom-street-east-ottawa-*********-1-p480.jpg?2025-08-09+01%3A32%3A06"
            width="480"
            height="320"
            alt="Townhouse for sale at 91 Heirloom St E Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069806"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/103-magnolia-way-east" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">103 Magnolia Way E</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>North Grenville Twp (Kemptville South)</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.040146"><meta itemprop="longitude" content="-75.673241"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">8 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="819900">819,900</span></li><li class="xs-inline xs-mr1">2+1 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A22438503" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ENGEL & VOLKERS OTTAWA</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/103-magnolia-way-east" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/103-magnolia-way-east-ottawa-*********-1-p480.jpg?2025-08-09+00%3A16%3A06 480w,
            https://photos.zolo.ca/103-magnolia-way-east-ottawa-*********-1-p768.jpg?2025-08-09+00%3A16%3A06 768w,
            https://photos.zolo.ca/103-magnolia-way-east-ottawa-*********-1-p.jpg?2025-08-09+00%3A16%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/103-magnolia-way-east-ottawa-*********-1-p480.jpg?2025-08-09+00%3A16%3A06"
            src="https://photos.zolo.ca/103-magnolia-way-east-ottawa-*********-1-p480.jpg?2025-08-09+00%3A16%3A06"
            width="480"
            height="320"
            alt="House for sale at 103 Magnolia Wy E Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069765"
            ></a></div></article><!-- FLX ad 3 - Gallery Tile -->

<a href="https://www.zolo.ca/internal_tracking.php?utm_content=FLXc1-buy&utm_campaign=gallery-tile&utm_source=zolo&utm_medium=website&utm_terms=&target_url=https%3A%2F%2Fflexiti.com%2Fnetwork" rel="nofollow" target="_blank">
	<style>
		.custom-title {
			font-size: 40px;
			font-weight: 700;
			line-height: 1.1;
		}

		.custom-subtitle {
			font-size: 20px;
			font-weight: 600;
			line-height: 1.4;
		}

		.custom-ad-wrapper {
			padding: 24px;
			line-height: 1;
			font-family: 'Open Sans';
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			border-radius: 8px;
		}

		@media (max-width: 768px) {
			.custom-ad-wrapper {
				gap:20px;
			}
		}

		.flx-color {
			color: #E94679;
		}

		.flx-bg-color {
			background-color: #FFFFFF;
			color: #30383D !important;
			letter-spacing: 0.01em;
		}

		.flx-ad-bg-color {
			background-color: #30383D;
		}

		.flx-bg-color:hover {
			color: #30383D !important;
		}
	</style>
	<div class="flx-ad-bg-color qm-gallery-ad-wrapper custom-ad-wrapper" data-cy="ad_tile">
		<!-- Logo -->
		<div class="row">
			<svg width="102" height="32" viewBox="0 0 102 32" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M57.2096 13.2683L54.6671 9.57373H47.4766L54.3096 19.5452C55.1637 17.4595 56.137 15.2348 57.2096 13.2683Z" fill="#E94679"/>
				<path d="M16.4102 31.2447V-0.000488281H22.3493V31.2248H16.4102V31.2447Z" fill="white"/>
				<path d="M89.3127 24.8684C89.3127 25.9013 89.6901 26.1794 91.0011 26.2787H92.034V31.2446H87.3065C84.4065 31.1453 83.3338 29.9734 83.3736 27.1329V14.4799H80.8906V9.55374H83.3736V3.6543H89.3127V9.55374H92.034V14.4799H89.3127V24.8684Z" fill="white"/>
				<path d="M101.028 3.43602C101.028 5.26346 99.618 6.67376 97.7508 6.67376C95.824 6.67376 94.4336 5.26346 94.4336 3.3963C94.4336 1.46954 95.8439 0.0791016 97.7508 0.0791016C99.6577 0.0791016 101.028 1.46954 101.028 3.43602ZM100.691 31.2448H94.7514V9.55396H100.691V31.2448Z" fill="white"/>
				<path d="M13.3124 5.48178V-0.000529196H11.187C5.24785 -0.1793 2.38752 2.54199 2.38752 8.34211V9.55378H0.00390625V14.5594H2.38752V31.2248H8.34655V14.5792H13.3124V9.55378H8.34655V8.34211C8.34655 6.7729 8.92259 5.9585 10.3925 5.62082C10.8096 5.52151 11.2267 5.48178 11.6637 5.48178C12.0809 5.48178 13.3124 5.48178 13.3124 5.48178Z" fill="white"/>
				<path d="M31.1697 21.8297C31.6862 24.8291 33.891 26.6963 36.8308 26.6963C39.0356 26.6963 40.9028 25.6236 41.8364 23.8955L47.3187 25.3853C45.1734 29.5963 41.3795 31.9799 36.7911 31.9799C29.8985 31.9799 24.9922 27.1134 24.9922 20.3598C24.9922 13.5665 29.7197 8.79932 36.4137 8.79932C43.346 8.79932 48.1728 13.6659 48.1728 20.6379L48.1331 21.8099H31.1697V21.8297ZM42.035 18.4132C41.5186 15.5529 39.4528 13.8248 36.513 13.8248C33.6526 13.8248 31.7458 15.4734 31.1697 18.4132H42.035Z" fill="white"/>
				<path d="M63.2854 16.9822C64.2984 14.837 66.1457 12.3739 68.0724 11.3807C68.9464 10.9239 69.6019 10.8643 70.8136 10.8246C70.8136 10.8246 70.7937 10.8246 70.7937 10.8047C68.9266 9.81153 67.3176 8.89781 65.6491 8.83822C65.4505 8.83822 65.232 8.81836 65.0333 8.81836C63.8813 8.81836 62.6696 8.97727 61.6565 9.59303C58.3195 11.6787 55.5783 20.0015 53.7509 23.8351C52.7379 25.9804 50.8906 28.4434 48.9638 29.4366C48.0898 29.8935 47.4343 29.9531 46.2227 29.9928C46.2227 29.9928 46.2425 29.9928 46.2425 30.0126C48.1097 30.986 49.7186 31.8997 51.3673 31.9791C51.5659 31.9791 51.7844 31.999 51.9831 31.999C53.1351 31.999 54.3468 31.8401 55.3598 31.2243C58.7168 29.1188 61.4579 20.796 63.2854 16.9822Z" fill="#E94679"/>
				<path d="M78.6181 3.43602C78.6181 5.26346 77.2078 6.67376 75.3406 6.67376C73.4139 6.67376 72.0234 5.26346 72.0234 3.3963C72.0234 1.46954 73.4337 0.0791016 75.3406 0.0791016C77.2475 0.0791016 78.6181 1.46954 78.6181 3.43602ZM78.2804 31.2448H72.3413V9.55396H78.2804V31.2448Z" fill="white"/>
				<path d="M59.8125 27.5498L62.355 31.2444H69.5654L62.7324 21.2729C61.8584 23.3387 60.8851 25.5833 59.8125 27.5498Z" fill="#E94679"/>
			</svg>
		</div>
		<!-- Title -->
		<div class="row">
			<span class="text-white custom-title">Moving soon?</span><br>
			<span class="flx-color custom-title">Furnish it with Flexiti!</span>
		</div>
		<!-- Subtitle -->
		<div class="row custom-subtitle text-white">
			<span>Shop with flexible financing plans.</span>
		</div>
		<!-- Subtitle -->
		<div class="row">
			<span class="qm-primary-btn-new flx-bg-color">Find a store</span>
		</div>
	</div>
</a><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/655-gardenvale-road-west" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">655 Gardenvale Road W</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Castle Heights/Rideau High</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.433666"><meta itemprop="longitude" content="-75.638763"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">8 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="1190000">1,190,000</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">1100-1500 sqft</li><li class="xs-inline">31-50 Years Old</li></ul>
        <button data-favorite="A22438438" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>EXP REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/655-gardenvale-road-west" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/655-gardenvale-road-west-ottawa-*********-1-p480.jpg?2025-08-08+23%3A47%3A05 480w,
            https://photos.zolo.ca/655-gardenvale-road-west-ottawa-*********-2-p768.jpg?2025-08-08+23%3A47%3A06 768w,
            https://photos.zolo.ca/655-gardenvale-road-west-ottawa-*********-1-p.jpg?2025-08-08+23%3A47%3A05 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/655-gardenvale-road-west-ottawa-*********-1-p480.jpg?2025-08-08+23%3A47%3A05"
            src="https://photos.zolo.ca/655-gardenvale-road-west-ottawa-*********-1-p480.jpg?2025-08-08+23%3A47%3A05"
            width="480"
            height="320"
            alt="House for sale at 655 Gardenvale Rd W Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069735"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/995-fieldfair-way" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">995 Fieldfair Way</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Notting Hill/Summerside</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.460049"><meta itemprop="longitude" content="-75.453827"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">10 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="989900">989,900</span></li><li class="xs-inline xs-mr1">4+1 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">2500-3000 sqft</li><li class="xs-inline">16-30 Years Old</li></ul>
        <button data-favorite="A2666495" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>CENTURY 21 ACTION POWER TEAM LTD.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/995-fieldfair-way" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/995-fieldfair-way-ottawa-*********-1-p480.jpg?2025-08-08+21%3A30%3A07 480w,
            https://photos.zolo.ca/995-fieldfair-way-ottawa-*********-1-p768.jpg?2025-08-08+21%3A30%3A07 768w,
            https://photos.zolo.ca/995-fieldfair-way-ottawa-*********-1-p.jpg?2025-08-08+21%3A30%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/995-fieldfair-way-ottawa-*********-1-p480.jpg?2025-08-08+21%3A30%3A07"
            src="https://photos.zolo.ca/995-fieldfair-way-ottawa-*********-1-p480.jpg?2025-08-08+21%3A30%3A07"
            width="480"
            height="320"
            alt="House for sale at 995 Fieldfair Wy Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069515"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/10-campbell-reid-court" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">10 Campbell Reid Court</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Kanata - Rural Kanata (Central)</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.373417"><meta itemprop="longitude" content="-75.956642"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">Open: Sat Aug 9, 2-4</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">11 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="675000">675,000</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1100-1500 sqft</li></ul>
        <button data-favorite="A15057851" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>FIRST CHOICE REALTY ONTARIO LTD.</div></div><script type="application/ld+json">{"@context": "http://schema.org","@type": "Event","name": "Open House 2:00 PM - 4:00 PM","url": "https://www.zolo.ca/ottawa-real-estate/10-campbell-reid-court","image": "https://photos.zolo.ca/10-campbell-reid-court-ottawa-*********-1-p.jpg?2025-08-08+21%3A17%3A06","description": "Open house at 2:00 PM Saturday August 9, 10 Campbell Reid Court, Ottawa.","startDate": "2025-08-09T14:00:00","endDate": "2025-08-09T16:00:00","location": {"@type": "Place","name": "10 Campbell Reid Court","address": {"@type": "PostalAddress","streetAddress": "10 Campbell Reid Court","addressLocality": "Ottawa","addressRegion": "ON","postalCode": "K2K 1X7"}}}</script></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/10-campbell-reid-court" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/10-campbell-reid-court-ottawa-*********-1-p480.jpg?2025-08-08+21%3A17%3A07 480w,
            https://photos.zolo.ca/10-campbell-reid-court-ottawa-*********-1-p768.jpg?2025-08-08+21%3A17%3A07 768w,
            https://photos.zolo.ca/10-campbell-reid-court-ottawa-*********-1-p.jpg?2025-08-08+21%3A17%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/10-campbell-reid-court-ottawa-*********-1-p480.jpg?2025-08-08+21%3A17%3A07"
            src="https://photos.zolo.ca/10-campbell-reid-court-ottawa-*********-1-p480.jpg?2025-08-08+21%3A17%3A07"
            width="480"
            height="320"
            alt="House for sale at 10 Campbell Reid Ct Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069468"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/732-cap-diamant-way" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">732 Cap Diamant Way</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Camelot</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.499557"><meta itemprop="longitude" content="-75.466866"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">11 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="729000">729,000</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">2000-2500 sqft</li></ul>
        <button data-favorite="A21837787" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>EASY LIST REALTY LTD.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/732-cap-diamant-way" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/732-cap-diamant-way-ottawa-*********-1-p480.jpg?2025-08-08+20%3A59%3A06 480w,
            https://photos.zolo.ca/732-cap-diamant-way-ottawa-*********-1-p768.jpg?2025-08-08+20%3A59%3A06 768w,
            https://photos.zolo.ca/732-cap-diamant-way-ottawa-*********-1-p.jpg?2025-08-08+20%3A59%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/732-cap-diamant-way-ottawa-*********-1-p480.jpg?2025-08-08+20%3A59%3A06"
            src="https://photos.zolo.ca/732-cap-diamant-way-ottawa-*********-1-p480.jpg?2025-08-08+20%3A59%3A06"
            width="480"
            height="320"
            alt="Townhouse for sale at 732 Cap Diamant Wy Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069461"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/200-lett-street/311" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">311-200 Lett Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>WEST CENTRE TOWN</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.416878"><meta itemprop="longitude" content="-75.712288"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Condo</title><path d="M12 7v-4h-6v18h12v-14h-6zM10 19h-2v-2h2v2zM10 15h-2v-2h2v2zM10 11h-2v-2h2v2zM10 7.2h-2v-2h2v2zM16 19h-2v-2h2v2zM16 15h-2v-2h2v2zM16 11h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">11 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="599900">599,900</span></li><li class="xs-inline xs-mr1">2 bed</li><li class="xs-inline xs-mr1">1 bath</li><li class="xs-inline xs-mr1">1000-1199 sqft</li></ul>
        <button data-favorite="A7966014" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX HALLMARK REALTY GROUP</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/200-lett-street/311" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/311-200-lett-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A46%3A06 480w,
            https://photos.zolo.ca/311-200-lett-street-ottawa-*********-1-p768.jpg?2025-08-08+20%3A46%3A06 768w,
            https://photos.zolo.ca/311-200-lett-street-ottawa-*********-1-p.jpg?2025-08-08+20%3A46%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/311-200-lett-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A46%3A06"
            src="https://photos.zolo.ca/311-200-lett-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A46%3A06"
            width="480"
            height="320"
            alt="Condo for sale at 200 Lett St Unit 311 Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069443"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/3062-lemay-circle" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">3062 Lemay Circle</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Town of Rockland</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.535137"><meta itemprop="longitude" content="-75.301971"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">11 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="599900">599,900</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">2000-2500 sqft</li></ul>
        <button data-favorite="A22437798" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE INTEGRITY REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/3062-lemay-circle" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/3062-lemay-circle-ottawa-*********-1-p480.jpg?2025-08-08+20%3A45%3A06 480w,
            https://photos.zolo.ca/3062-lemay-circle-ottawa-*********-1-p768.jpg?2025-08-08+20%3A45%3A06 768w,
            https://photos.zolo.ca/3062-lemay-circle-ottawa-*********-1-p.jpg?2025-08-08+20%3A45%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/3062-lemay-circle-ottawa-*********-1-p480.jpg?2025-08-08+20%3A45%3A06"
            src="https://photos.zolo.ca/3062-lemay-circle-ottawa-*********-1-p480.jpg?2025-08-08+20%3A45%3A06"
            width="480"
            height="320"
            alt="House for sale at 3062 Lemay Circ Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069442"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/161-rugosa-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">161 Rugosa Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Barrhaven - Half Moon Bay</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.237225"><meta itemprop="longitude" content="-75.746124"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">Open: Sat Aug 9, 2-4</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="839990">839,990</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">2000-2500 sqft</li><li class="xs-inline">0-5 Years Old</li></ul>
        <button data-favorite="A21735821" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>AVENUE NORTH REALTY INC.</div></div><script type="application/ld+json">{"@context": "http://schema.org","@type": "Event","name": "Open House 2:00 PM - 4:00 PM","url": "https://www.zolo.ca/ottawa-real-estate/161-rugosa-street","image": "https://photos.zolo.ca/161-rugosa-street-ottawa-*********-1-p.jpg?2025-08-08+20%3A17%3A05","description": "Open house at 2:00 PM Saturday August 9, 161 Rugosa Street, Ottawa.","startDate": "2025-08-09T14:00:00","endDate": "2025-08-09T16:00:00","location": {"@type": "Place","name": "161 Rugosa Street","address": {"@type": "PostalAddress","streetAddress": "161 Rugosa Street","addressLocality": "Ottawa","addressRegion": "ON","postalCode": "K2J 6X4"}}}</script></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/161-rugosa-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/161-rugosa-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A17%3A06 480w,
            https://photos.zolo.ca/161-rugosa-street-ottawa-*********-1-p768.jpg?2025-08-08+20%3A17%3A05 768w,
            https://photos.zolo.ca/161-rugosa-street-ottawa-*********-1-p.jpg?2025-08-08+20%3A17%3A05 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/161-rugosa-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A17%3A06"
            src="https://photos.zolo.ca/161-rugosa-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A17%3A06"
            width="480"
            height="320"
            alt="House for sale at 161 Rugosa St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069387"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/5448-otto-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">5448 Otto Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Osgoode</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.141296"><meta itemprop="longitude" content="-75.612381"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="499900">499,900</span></li><li class="xs-inline xs-mr1">2 bed</li><li class="xs-inline xs-mr1">1 bath</li><li class="xs-inline xs-mr1">700-1100 sqft</li><li class="xs-inline">100+ Years Old</li></ul>
        <button data-favorite="A21915549" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>EXIT EXCEL REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/5448-otto-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/5448-otto-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A00%3A12 480w,
            https://photos.zolo.ca/5448-otto-street-ottawa-*********-1-p768.jpg?2025-08-08+20%3A00%3A12 768w,
            https://photos.zolo.ca/5448-otto-street-ottawa-*********-1-p.jpg?2025-08-08+20%3A00%3A11 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/5448-otto-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A00%3A12"
            src="https://photos.zolo.ca/5448-otto-street-ottawa-*********-1-p480.jpg?2025-08-08+20%3A00%3A12"
            width="480"
            height="320"
            alt="House for sale at 5448 Otto St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069343"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/59-woodford-way" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">59 Woodford Way</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Barrhaven - Longfields</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.290863"><meta itemprop="longitude" content="-75.733017"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="889900">889,900</span></li><li class="xs-inline xs-mr1">3+2 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">2000-2500 sqft</li></ul>
        <button data-favorite="A22437628" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ONE PERCENT REALTY LTD.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/59-woodford-way" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/59-woodford-way-ottawa-*********-1-p480.jpg?2025-08-08+19%3A48%3A08 480w,
            https://photos.zolo.ca/59-woodford-way-ottawa-*********-1-p768.jpg?2025-08-08+19%3A48%3A07 768w,
            https://photos.zolo.ca/59-woodford-way-ottawa-*********-1-p.jpg?2025-08-08+19%3A48%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/59-woodford-way-ottawa-*********-1-p480.jpg?2025-08-08+19%3A48%3A08"
            src="https://photos.zolo.ca/59-woodford-way-ottawa-*********-1-p480.jpg?2025-08-08+19%3A48%3A08"
            width="480"
            height="320"
            alt="House for sale at 59 Woodford Wy Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069333"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/92-lamadeleine-boulevard" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">92 Lamadeleine Boulevard</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Embrun</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.278244"><meta itemprop="longitude" content="-75.285011"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="624900">624,900</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li><li class="xs-inline">16-30 Years Old</li></ul>
        <button data-favorite="A22437616" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>INNOVATION REALTY LTD.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/92-lamadeleine-boulevard" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/92-lamadeleine-boulevard-ottawa-*********-1-p480.jpg?2025-08-08+19%3A45%3A07 480w,
            https://photos.zolo.ca/92-lamadeleine-boulevard-ottawa-*********-1-p768.jpg?2025-08-08+19%3A45%3A07 768w,
            https://photos.zolo.ca/92-lamadeleine-boulevard-ottawa-*********-1-p.jpg?2025-08-08+19%3A45%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/92-lamadeleine-boulevard-ottawa-*********-1-p480.jpg?2025-08-08+19%3A45%3A07"
            src="https://photos.zolo.ca/92-lamadeleine-boulevard-ottawa-*********-1-p480.jpg?2025-08-08+19%3A45%3A07"
            width="480"
            height="320"
            alt="House for sale at 92 Lamadeleine Blvd Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="********"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/7122-bank-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">7122 Bank Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Osgoode Twp North of Reg Rd 6</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.22385"><meta itemprop="longitude" content="-75.493889"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="1980000">1,980,000</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A21431889" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX HALLMARK REALTY GROUP</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/7122-bank-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p480.jpg?2025-08-08+19%3A45%3A06 480w,
            https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p768.jpg?2025-08-08+19%3A45%3A06 768w,
            https://photos.zolo.ca/7122-bank-street-ottawa-*********-1-p.jpg?2025-08-08+19%3A45%3A05 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p480.jpg?2025-08-08+19%3A45%3A06"
            src="https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p480.jpg?2025-08-08+19%3A45%3A06"
            width="480"
            height="320"
            alt="House for sale at 7122 Bank St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="********"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/7122-bank-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">7122 Bank Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Osgoode Twp North of Reg Rd 6</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.22385"><meta itemprop="longitude" content="-75.493889"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="1980000">1,980,000</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A21431889" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX HALLMARK REALTY GROUP</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/7122-bank-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p480.jpg?2025-08-08+19%3A45%3A06 480w,
            https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p768.jpg?2025-08-08+19%3A45%3A06 768w,
            https://photos.zolo.ca/7122-bank-street-ottawa-*********-1-p.jpg?2025-08-08+19%3A45%3A05 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p480.jpg?2025-08-08+19%3A45%3A06"
            src="https://photos.zolo.ca/7122-bank-street-ottawa-*********-2-p480.jpg?2025-08-08+19%3A45%3A06"
            width="480"
            height="320"
            alt="House for sale at 7122 Bank St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="********"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/2025-blossite-private" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">2025 Blossite Private</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Barrhaven - Heritage Park</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.263649"><meta itemprop="longitude" content="-75.745705"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="499990">499,990</span></li><li class="xs-inline xs-mr1">2+1 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1000-1199 sqft</li></ul>
        <button data-favorite="A22437605" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE TEAM REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/2025-blossite-private" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/2025-blossite-private-ottawa-*********-1-p480.jpg?2025-08-08+19%3A58%3A07 480w,
            https://photos.zolo.ca/2025-blossite-private-ottawa-*********-1-p768.jpg?2025-08-08+19%3A58%3A07 768w,
            https://photos.zolo.ca/2025-blossite-private-ottawa-*********-1-p.jpg?2025-08-08+19%3A58%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/2025-blossite-private-ottawa-*********-1-p480.jpg?2025-08-08+19%3A58%3A07"
            src="https://photos.zolo.ca/2025-blossite-private-ottawa-*********-1-p480.jpg?2025-08-08+19%3A58%3A07"
            width="480"
            height="320"
            alt="Townhouse for sale at 2025 Blossite Pt Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069299"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/738-river-road-north" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">738 River Road N</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Mcnab/Braeside Twps</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.459869"><meta itemprop="longitude" content="-76.400414"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">12 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="679900">679,900</span></li><li class="xs-inline xs-mr1">2 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A22437590" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>COLDWELL BANKER HERITAGE WAY REALTY INC.</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/738-river-road-north" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/738-river-road-north-ottawa-*********-1-p480.jpg?2025-08-08+19%3A31%3A06 480w,
            https://photos.zolo.ca/738-river-road-north-ottawa-*********-1-p768.jpg?2025-08-08+19%3A31%3A06 768w,
            https://photos.zolo.ca/738-river-road-north-ottawa-*********-1-p.jpg?2025-08-08+19%3A31%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/738-river-road-north-ottawa-*********-1-p480.jpg?2025-08-08+19%3A31%3A06"
            src="https://photos.zolo.ca/738-river-road-north-ottawa-*********-1-p480.jpg?2025-08-08+19%3A31%3A06"
            width="480"
            height="320"
            alt="House for sale at 738 River Rd N Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069264"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new vow"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/265-poulin-avenue/408" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress"><span class="priv">408-265</span> Poulin Avenue</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Britannia</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.366562"><meta itemprop="longitude" content="-75.793007"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Condo</title><path d="M12 7v-4h-6v18h12v-14h-6zM10 19h-2v-2h2v2zM10 15h-2v-2h2v2zM10 11h-2v-2h2v2zM10 7.2h-2v-2h2v2zM16 19h-2v-2h2v2zM16 15h-2v-2h2v2zM16 11h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">13 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer">$XXX,XXX</li><li class="xs-inline xs-mr1">&mdash; bed</li><li class="xs-inline xs-mr1">&mdash; bath</li><li class="xs-inline xs-mr1">&mdash; sqft</li><li class="xs-inline">&mdash; Years Old</li></ul><div class="fill-white xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 xs-absolute xs-t1 xs-r1 xs-z1 pointer hover:text-red" data-target="#signupModaltop" data-toggle="modal">
                    <svg height="24" viewBox="0 0 24 24" width="24" aria-hidden="true">
                        <path d="M18 8h-1V6A5 5 0 007 6v2H6a2 2 0 00-2 2v10c0 1.1.9 2 2 2h12a2 2 0 002-2V10a2 2 0 00-2-2zM9 6a3 3 0 116 0v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"></path>
                    </svg>
                </div><div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; <span class="priv">*********</span></span></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><div class="cover-reg-tile fill-secondary aspect-content xs-z2 pointer" data-target="#signupModaltop" data-toggle="modal">
                    <div class="xs-flex xs-flex-align-center xs-flex-justify-center xs-full-height">
                        <div class="text-white xs-text-center xs-full-width xs-px2" style="margin-top: -1rem;">
                            <div class="xs-mb05 bold">
                                <div class="xs-inline-block">
                                    <span class="decoration-underline">Join or Sign In</span> &bull;
                                </div>
                                <div class="xs-inline-block">See photos &amp; sold data</div>
                            </div>
                            <div class="xs-text-5">Real estate boards require a verified account</div>
                        </div>
                    </div>
                </div><a href="https://www.zolo.ca/ottawa-real-estate/265-poulin-avenue/408" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/408-265-poulin-avenue-ottawa-*********-1-p480.jpg?2025-08-08+19%3A16%3A07 480w,
            https://photos.zolo.ca/408-265-poulin-avenue-ottawa-*********-1-p768.jpg?2025-08-08+19%3A16%3A07 768w,
            https://photos.zolo.ca/408-265-poulin-avenue-ottawa-*********-1-p.jpg?2025-08-08+19%3A16%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/408-265-poulin-avenue-ottawa-*********-1-p480.jpg?2025-08-08+19%3A16%3A07"
            src="https://photos.zolo.ca/408-265-poulin-avenue-ottawa-*********-1-p480.jpg?2025-08-08+19%3A16%3A07"
            width="480"
            height="320"
            alt="Condo for sale at 265 Poulin Ave Unit 408 Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069212"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/1038-charolais-place" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">1038 Charolais Place</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Goulbourn Twp From Franktown Rd/South To Rideau</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.176594"><meta itemprop="longitude" content="-75.839424"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">13 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="869990">869,990</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">3000-3500 sqft</li><li class="xs-inline">New</li></ul>
        <button data-favorite="A22437550" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE TEAM REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/1038-charolais-place" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/1038-charolais-place-ottawa-*********-1-p480.jpg?2025-08-08+19%3A16%3A08 480w,
            https://photos.zolo.ca/1038-charolais-place-ottawa-*********-1-p768.jpg?2025-08-08+19%3A16%3A07 768w,
            https://photos.zolo.ca/1038-charolais-place-ottawa-*********-1-p.jpg?2025-08-08+19%3A16%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/1038-charolais-place-ottawa-*********-1-p480.jpg?2025-08-08+19%3A16%3A08"
            src="https://photos.zolo.ca/1038-charolais-place-ottawa-*********-1-p480.jpg?2025-08-08+19%3A16%3A08"
            width="480"
            height="320"
            alt="House for sale at 1038 Charolais Pl Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069211"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/14100-concession-1-2-road" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">14100 Concession 1-2 Road</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>North Stormont (Finch) Twp</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.101376"><meta itemprop="longitude" content="-75.156952"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">13 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="914900">914,900</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">3500-5000 sqft</li></ul>
        <button data-favorite="A22093915" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>COLDWELL BANKER FIRST OTTAWA REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/14100-concession-1-2-road" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/14100-concession-1-2-road-ottawa-*********-1-p480.jpg?2025-08-08+18%3A59%3A06 480w,
            https://photos.zolo.ca/14100-concession-1-2-road-ottawa-*********-1-p768.jpg?2025-08-08+18%3A59%3A06 768w,
            https://photos.zolo.ca/14100-concession-1-2-road-ottawa-*********-1-p.jpg?2025-08-08+18%3A59%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/14100-concession-1-2-road-ottawa-*********-1-p480.jpg?2025-08-08+18%3A59%3A06"
            src="https://photos.zolo.ca/14100-concession-1-2-road-ottawa-*********-1-p480.jpg?2025-08-08+18%3A59%3A06"
            width="480"
            height="320"
            alt="House for sale at 14100 Concession 1-2 Rd Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069141"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/2675-marie-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">2675 Marie Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Britannia</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.362614"><meta itemprop="longitude" content="-75.791924"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">13 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="699000">699,000</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1100-1500 sqft</li></ul>
        <button data-favorite="A10301135" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>SUTTON GROUP - OTTAWA REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/2675-marie-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/2675-marie-street-ottawa-*********-1-p480.jpg?2025-08-08+18%3A44%3A07 480w,
            https://photos.zolo.ca/2675-marie-street-ottawa-*********-1-p768.jpg?2025-08-08+18%3A44%3A07 768w,
            https://photos.zolo.ca/2675-marie-street-ottawa-*********-1-p.jpg?2025-08-08+18%3A44%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/2675-marie-street-ottawa-*********-1-p480.jpg?2025-08-08+18%3A44%3A07"
            src="https://photos.zolo.ca/2675-marie-street-ottawa-*********-1-p480.jpg?2025-08-08+18%3A44%3A07"
            width="480"
            height="320"
            alt="Townhouse for sale at 2675 Marie St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18069074"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/76-barrette-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">76 Barrette Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Vanier</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.439747"><meta itemprop="longitude" content="-75.674072"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">14 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="690000">690,000</span></li><li class="xs-inline xs-mr1">5 bed</li><li class="xs-inline xs-mr1">4 bath</li><li class="xs-inline xs-mr1">2000-2500 sqft</li></ul>
        <button data-favorite="A14312403" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX HALLMARK REALTY GROUP</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/76-barrette-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/76-barrette-street-ottawa-*********-1-p480.jpg?2025-08-08+18%3A04%3A07 480w,
            https://photos.zolo.ca/76-barrette-street-ottawa-*********-1-p768.jpg?2025-08-08+18%3A04%3A07 768w,
            https://photos.zolo.ca/76-barrette-street-ottawa-*********-1-p.jpg?2025-08-08+18%3A04%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/76-barrette-street-ottawa-*********-1-p480.jpg?2025-08-08+18%3A04%3A07"
            src="https://photos.zolo.ca/76-barrette-street-ottawa-*********-1-p480.jpg?2025-08-08+18%3A04%3A07"
            width="480"
            height="320"
            alt="Townhouse for sale at 76 Barrette St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18068922"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/213-romulus-private/107" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">107-213 Romulus Private</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Carson Meadows</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.444862"><meta itemprop="longitude" content="-75.622055"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">14 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="424900">424,900</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1400-1599 sqft</li></ul>
        <button data-favorite="A22437137" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ENGEL & VOLKERS OTTAWA</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/213-romulus-private/107" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/107-213-romulus-private-ottawa-*********-1-p480.jpg?2025-08-08+17%3A31%3A06 480w,
            https://photos.zolo.ca/107-213-romulus-private-ottawa-*********-1-p768.jpg?2025-08-08+17%3A31%3A06 768w,
            https://photos.zolo.ca/107-213-romulus-private-ottawa-*********-1-p.jpg?2025-08-08+17%3A31%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/107-213-romulus-private-ottawa-*********-1-p480.jpg?2025-08-08+17%3A31%3A06"
            src="https://photos.zolo.ca/107-213-romulus-private-ottawa-*********-1-p480.jpg?2025-08-08+17%3A31%3A06"
            width="480"
            height="320"
            alt="Townhouse for sale at 213 Romulus Pt Unit 107 Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18068785"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/36-du-moulin-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">36 Du Moulin Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Alfred</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.558273"><meta itemprop="longitude" content="-74.878349"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">15 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="425000">425,000</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A22437015" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX HALLMARK REALTY GROUP</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/36-du-moulin-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/36-du-moulin-street-ottawa-*********-1-p480.jpg?2025-08-08+17%3A03%3A07 480w,
            https://photos.zolo.ca/36-du-moulin-street-ottawa-*********-1-p768.jpg?2025-08-08+17%3A03%3A07 768w,
            https://photos.zolo.ca/36-du-moulin-street-ottawa-*********-1-p.jpg?2025-08-08+17%3A03%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/36-du-moulin-street-ottawa-*********-1-p480.jpg?2025-08-08+17%3A03%3A07"
            src="https://photos.zolo.ca/36-du-moulin-street-ottawa-*********-1-p480.jpg?2025-08-08+17%3A03%3A07"
            width="480"
            height="320"
            alt="House for sale at 36 Du Moulin St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18068624"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/404-premiere-lane" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">404 Premiere Lane</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Town of Rockland</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.399883"><meta itemprop="longitude" content="-75.744621"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">15 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="559000">559,000</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">1100-1500 sqft</li><li class="xs-inline">0-5 Years Old</li></ul>
        <button data-favorite="A22436897" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>RE/MAX HALLMARK REALTY GROUP</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/404-premiere-lane" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/404-premiere-lane-ottawa-*********-1-p480.jpg?2025-08-08+16%3A36%3A07 480w,
            https://photos.zolo.ca/404-premiere-lane-ottawa-*********-1-p768.jpg?2025-08-08+16%3A36%3A07 768w,
            https://photos.zolo.ca/404-premiere-lane-ottawa-*********-1-p.jpg?2025-08-08+16%3A36%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/404-premiere-lane-ottawa-*********-1-p480.jpg?2025-08-08+16%3A36%3A07"
            src="https://photos.zolo.ca/404-premiere-lane-ottawa-*********-1-p480.jpg?2025-08-08+16%3A36%3A07"
            width="480"
            height="320"
            alt="Townhouse for sale at 404 Premiere Ln Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18068498"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/299-sherwood-drive" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">299 Sherwood Drive</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Perry</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.470867"><meta itemprop="longitude" content="-79.178009"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>House</title><path d="M11 20v-3h2v3h6v-8h3l-10-9-10 9h3v8h6z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">15 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="749900">749,900</span></li><li class="xs-inline xs-mr1">4 bed</li><li class="xs-inline xs-mr1">1 bath</li><li class="xs-inline xs-mr1">700-1100 sqft</li></ul>
        <button data-favorite="A22436891" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>COMFREE</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/299-sherwood-drive" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/299-sherwood-drive-ottawa-*********-1-p480.jpg?2025-08-08+16%3A34%3A07 480w,
            https://photos.zolo.ca/299-sherwood-drive-ottawa-*********-2-p768.jpg?2025-08-08+16%3A34%3A09 768w,
            https://photos.zolo.ca/299-sherwood-drive-ottawa-*********-1-p.jpg?2025-08-08+16%3A34%3A07 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/299-sherwood-drive-ottawa-*********-1-p480.jpg?2025-08-08+16%3A34%3A07"
            src="https://photos.zolo.ca/299-sherwood-drive-ottawa-*********-1-p480.jpg?2025-08-08+16%3A34%3A07"
            width="480"
            height="320"
            alt="House for sale at 299 Sherwood Dr Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18068486"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/11-villandry-street" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">11 Villandry Street</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Barrhaven East</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.274792"><meta itemprop="longitude" content="-75.707947"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Townhouse</title><path d="M18 6l-3 3-3-3-3 3-3-3-4 4v10h3v-3h2v3h4v-3h2v3h4v-3h2v3h3v-10l-4-4zM7 14h-2v-2h2v2zM13 14h-2v-2h2v2zM19 14h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">16 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="639990">639,990</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">3 bath</li><li class="xs-inline xs-mr1">1500-2000 sqft</li></ul>
        <button data-favorite="A8552613" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE TEAM REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/11-villandry-street" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/11-villandry-street-ottawa-*********-1-p480.jpg?2025-08-08+16%3A19%3A07 480w,
            https://photos.zolo.ca/11-villandry-street-ottawa-*********-1-p768.jpg?2025-08-08+16%3A19%3A06 768w,
            https://photos.zolo.ca/11-villandry-street-ottawa-*********-1-p.jpg?2025-08-08+16%3A19%3A06 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/11-villandry-street-ottawa-*********-1-p480.jpg?2025-08-08+16%3A19%3A07"
            src="https://photos.zolo.ca/11-villandry-street-ottawa-*********-1-p480.jpg?2025-08-08+16%3A19%3A07"
            width="480"
            height="320"
            alt="Townhouse for sale at 11 Villandry St Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18068391"
            ></a></div></article><article class="card-listing xs-relative xs-full-height xs-flex xs-flex-column-reverse xs-flex-justify-end rounded shadow-card xs-overflow-hidden new"><div class="card-listing--details xs-p2 xs-text-4 fill-white flex xs-flex-column xs-flex-shrink-0 xs-relative"><div class="card-listing--location text-5 truncate xs-flex-order-2" itemscope itemtype="http://schema.org/SingleFamilyResidence"><a href="https://www.zolo.ca/ottawa-real-estate/415-greenview-avenue/404" class="address text-primary" itemprop="address" itemscope itemtype="http://schema.org/PostalAddress"><h3 class="card-listing--location text-5 xs-inline"><span class="street" itemprop="streetAddress">404-415 Greenview Avenue</span>, <span class="city" itemprop="addressLocality">Ottawa</span>, <span class="province" itemprop="addressRegion">ON</span></h3></a><span class="neighbourhood"><span class="xs-mx05">&bull;</span>Britannia</span><span itemprop="geo" itemscope="" itemtype="http://schema.org/GeoCoordinates"><meta itemprop="latitude" content="45.360325"><meta itemprop="longitude" content="-75.794472"></span></div><div class="xs-line-height-1 xs-absolute xs-l2 xs-r2 xs-z2 pointer-events-none xs-flex xs-flex-align-center xs-flex-justify-space-between" style="top:-2rem;"><div class="fill-green text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-mr-auto"><svg viewBox="0 0 24 24" height="12" width="12" class="fill-current xs-mr05"><title>Condo</title><path d="M12 7v-4h-6v18h12v-14h-6zM10 19h-2v-2h2v2zM10 15h-2v-2h2v2zM10 11h-2v-2h2v2zM10 7.2h-2v-2h2v2zM16 19h-2v-2h2v2zM16 15h-2v-2h2v2zM16 11h-2v-2h2v2z"/></svg>For Sale</div><div class="fill-primary text-white pill xs-text-6 xs-line-height-1 bold xs-inline-flex xs-flex-align-center xs-py05 xs-px1 xs-ml05">16 hours</div></div><ul class="card-listing--values truncate list-unstyled xs-flex-order-1 xs-mb05"><li class="price xs-block xs-mb1 text-2 heavy" itemprop="offers" itemscope="" itemtype="http://schema.org/Offer"><span itemprop="priceCurrency" content="CAD">$</span><span itemprop="price" value="429000">429,000</span></li><li class="xs-inline xs-mr1">3 bed</li><li class="xs-inline xs-mr1">2 bath</li><li class="xs-inline xs-mr1">1000-1199 sqft</li><li class="xs-inline">31-50 Years Old</li></ul>
        <button data-favorite="A22436848" aria-label="Save" class="favorite fill-white xs-p0 xs-border-none circle xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-40 xs-w-40 hover:text-red pointer  xs-absolute xs-t1 xs-r1 xs-z1">
            <svg viewBox="0 0 24 24" height="24" width="24">
                <path class="save-heart-fill fill-current text-white" d="M15.16 16.64l-.51.47c-.84.79-1.84 1.7-2.65 2.42-.8-.73-1.8-1.63-2.65-2.42l-.51-.46C7.24 15.2 3 11.36 3 8.07a3.94 3.94 0 013.94-3.94 4.47 4.47 0 013.27 1.48L12 7.52l1.78-1.9a4.51 4.51 0 013.28-1.47A3.94 3.94 0 0121 8.08c0 3.29-4.24 7.12-5.84 8.56z"/>
                <path class="save-heart-outline fill-current" d="M16.5 3C13.6 3 12 5.09 12 5.09S10.4 3 7.5 3A5.5 5.5 0 002 8.5c0 4.17 4.91 8.21 6.28 9.49C9.86 19.46 12 21.35 12 21.35s2.14-1.89 3.72-3.36C17.09 16.71 22 12.67 22 8.5A5.5 5.5 0 0016.5 3zm-1.69 13.11l-.46.42c-.75.7-1.63 1.5-2.35 2.14a175.71 175.71 0 01-2.81-2.56C7.77 14.83 4 11.41 4 8.5A3.5 3.5 0 017.5 5c1.83 0 2.89 1.27 2.91 1.3L12 8l1.59-1.7A4.01 4.01 0 0116.5 5 3.5 3.5 0 0120 8.5c0 2.92-3.77 6.33-5.19 7.61z"/>
            </svg>
        </button>
        <div class="xs-mt2 xs-flex xs-flex-order-3 text-6 text-muted heavy nowrap"><span class="xs-inline-block">MLS&reg; *********</span><div class="card-listing--brokerage truncate"><span class="xs-mx05">&bull;</span>ROYAL LEPAGE PERFORMANCE REALTY</div></div></div><div class="card-listing--image fill-white xs-relative xs-overflow-hidden"><a href="https://www.zolo.ca/ottawa-real-estate/415-greenview-avenue/404" class="xs-block xs-aspect-3-2 fill-grey-bg-3 card-listing--image-link"><img
            sizes="
            (min-width:75rem) calc(33.333vw - 6rem / 3),
            (min-width:48rem) calc(50vw - 4.5rem / 2),
            100vw"
            data-srcset="
            https://photos.zolo.ca/404-415-greenview-avenue-ottawa-*********-1-p480.jpg?2025-08-08+16%3A16%3A26 480w,
            https://photos.zolo.ca/404-415-greenview-avenue-ottawa-*********-1-p768.jpg?2025-08-08+16%3A16%3A26 768w,
            https://photos.zolo.ca/404-415-greenview-avenue-ottawa-*********-1-p.jpg?2025-08-08+16%3A16%3A26 1200w"
            srcset="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
            data-img-defer-src="https://photos.zolo.ca/404-415-greenview-avenue-ottawa-*********-1-p480.jpg?2025-08-08+16%3A16%3A26"
            src="https://photos.zolo.ca/404-415-greenview-avenue-ottawa-*********-1-p480.jpg?2025-08-08+16%3A16%3A26"
            width="480"
            height="320"
            alt="Condo for sale at 415 Greenview Ave Unit 404 Ottawa Ontario - MLS: *********"
            class="card-listing--img aspect-content object-fit-cover  xs-full-width xs-full-height xs-z1 js-img-defer js-modal-listing"
            id="18068351"
            ></a></div></article>    </div>
</section>

    <section class="supplementary-nav xs-my6 xs-flex xs-flex-column xs-flex-align-center">
                                    <div class="xs-flex-grow-1 xs-mb3">
                    Showing results <span class="xs-block"><strong>1 — 35</strong> of <strong>6340</strong></span>
                </div>

            <nav class="xs-hide md-flex xs-mb5">
                                            <a href="https://www.zolo.ca/ottawa-real-estate" class="button button--large button--mono xs-mr1 button--active">1</a>
                                                    <a href="https://www.zolo.ca/ottawa-real-estate/page-2" class="button button--mono button--large xs-mr1 ">2</a>
                                                    <a href="https://www.zolo.ca/ottawa-real-estate/page-3" class="button button--mono button--large xs-mr1 ">3</a>
                                                    <a href="https://www.zolo.ca/ottawa-real-estate/page-4" class="button button--mono button--large xs-mr1 ">4</a>
                                                    <a href="https://www.zolo.ca/ottawa-real-estate/page-5" class="button button--mono button--large xs-mr1 ">5</a>
                                                                    <span class="button button--mono button--large button--disabled pointer-events-none xs-mr1">&hellip;</span>
                                        <a href="https://www.zolo.ca/ottawa-real-estate/page-182" class="button button--mono button--large xs-mr1">182</a>
                    <a href="https://www.zolo.ca/ottawa-real-estate/page-2" class="button button--mono button--large xs-flex xs-flex-align-center xs-mr1" aria-label="next page of results"><svg viewBox="0 0 24 24" height="24" width="24" class="fill-current" aria-hidden="true"><path d="M10 5.93l-1.5 1.5L13.07 12 8.5 16.57l1.5 1.5L16.07 12 10 5.93z"/></svg></a>
                            </nav>

            <nav class="pager xs-inline-grid xs-grid-cols-2 xs-gap-2 md-hide xs-mb5">
                                    <div class="button button--mono button--large button--disabled xs-flex xs-flex-align-center pointer-events-none" aria-hidden="true"><svg viewBox="0 0 24 24" height="24" width="24" class="fill-current"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg></div>
                                                    <a class="button button--mono button--large xs-flex xs-flex-align-center" href="https://www.zolo.ca/ottawa-real-estate/page-2" aria-label="next page of results"><svg viewBox="0 0 24 24" height="24" width="24" class="fill-current" aria-hidden="true"><path d="M10 5.93l-1.5 1.5L13.07 12 8.5 16.57l1.5 1.5L16.07 12 10 5.93z"/></svg></a>
                            </nav>

        <!-- FLX ad 2 - Footer -->
<section class="xs-mb5 xs-border-top xs-border-bottom" data-cy="pagination_ad">
	<div class="container xs-py3 xs-text-center">
		<div class="xs-text-center md-text-left xs-inline-block md-inline xs-col-12 md-col-9">
			<a href="https://www.zolo.ca/internal_tracking.php?utm_content=FLXb1-buy&utm_campaign=gallery-pagination&utm_source=zolo&utm_medium=website&utm_terms=&target_url=https%3A%2F%2Fflexiti.com%2Fnetwork" rel="nofollow" target="_blank">
				<span class="text-primary xs-block md-inline">Shop with flexible financing plans at over 8,500 retail
					locations, in-store and online with Flexiti.</span>
				<span class="text-primary heavy decoration-underline">Find a store </span>
			</a>
			<p class="text xs-mt2 xs-text-6 xs-text-center">Conditions Apply.</p>
			<div class="xs-text-center xs-inline xs-pb2">
				<svg version="1.1" height="25" xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 508.6 161.1"
					xml:space="preserve">
					<style>
						.st0 {
							fill: #e94679
						}

						.st2 {
							fill: #343c3c
						}
					</style>
					<path class="st0" d="m288 66.8-12.8-18.6H239l34.4 50.2c4.3-10.5 9.2-21.7 14.6-31.6z"></path>
					<path class="st2"
						d="M82.6 157.3V0h29.9v157.2H82.6v.1zM449.6 125.2c0 5.2 1.9 6.6 8.5 7.1h5.2v25h-23.8c-14.6-.5-20-6.4-19.8-20.7V72.9h-12.5V48.1h12.5V18.4h29.9v29.7h13.7v24.8h-13.7v52.3zM508.6 17.3c0 9.2-7.1 16.3-16.5 16.3-9.7 0-16.7-7.1-16.7-16.5 0-9.7 7.1-16.7 16.7-16.7s16.5 7 16.5 16.9zm-1.7 140H477V48.1h29.9v109.2zM67 27.6V0H56.3C26.4-.9 12 12.8 12 42v6.1H0v25.2h12v83.9h30V73.4h25V48.1H42V42c0-7.9 2.9-12 10.3-13.7 2.1-.5 4.2-.7 6.4-.7H67zM156.9 109.9a27.9 27.9 0 0 0 28.5 24.5 28 28 0 0 0 25.2-14.1l27.6 7.5a57.8 57.8 0 0 1-53 33.2c-34.7 0-59.4-24.5-59.4-58.5 0-34.2 23.8-58.2 57.5-58.2 34.9 0 59.2 24.5 59.2 59.6l-.2 5.9h-85.4v.1zm54.7-17.2a26.6 26.6 0 0 0-27.8-23.1c-14.4 0-24 8.3-26.9 23.1h54.7z">
					</path>
					<path class="st0"
						d="M318.6 85.5c5.1-10.8 14.4-23.2 24.1-28.2a28 28 0 0 1 13.8-2.8l-.1-.1c-9.4-5-17.5-9.6-25.9-9.9l-3.1-.1c-5.8 0-11.9.8-17 3.9-16.8 10.5-30.6 52.4-39.8 71.7-5.1 10.8-14.4 23.2-24.1 28.2a28 28 0 0 1-13.8 2.8l.1.1c9.4 4.9 17.5 9.5 25.8 9.9l3.1.1c5.8 0 11.9-.8 17-3.9 16.9-10.6 30.7-52.5 39.9-71.7z">
					</path>
					<path class="st2"
						d="M395.8 17.3c0 9.2-7.1 16.3-16.5 16.3-9.7 0-16.7-7.1-16.7-16.5 0-9.7 7.1-16.7 16.7-16.7s16.5 7 16.5 16.9zm-1.7 140h-29.9V48.1h29.9v109.2z">
					</path>
					<path class="st0" d="m301.1 138.7 12.8 18.6h36.3l-34.4-50.2a362.5 362.5 0 0 1-14.7 31.6z"></path>
				</svg>
			</div>
		</div>
	</div>
</section>
        <ol class="list-unstyled xs-text-5 xs-inline-flex xs-flex-align-center xs-flex-wrap">

            <li class="xs-inline-flex xs-flex-align-center"><a class="text-primary hover:decoration-underline xs-py05" href="/">Canada</a></li><li class="xs-inline-flex xs-flex-align-center"><svg viewBox="0 0 24 24" height="16" width="16" class="fill-current xs-mx05" aria-hidden="true"><path d="M10 5.93l-1.5 1.5L13.07 12 8.5 16.57l1.5 1.5L16.07 12 10 5.93z"/></svg><a class="text-primary hover:decoration-underline xs-py05" href="/ontario-real-estate">Ontario</a></li><li class="xs-inline-flex xs-flex-align-center"><svg viewBox="0 0 24 24" height="16" width="16" class="fill-current xs-mx05" aria-hidden="true"><path d="M10 5.93l-1.5 1.5L13.07 12 8.5 16.57l1.5 1.5L16.07 12 10 5.93z"/></svg><a class="text-primary hover:decoration-underline xs-py05" href="/ottawa-real-estate">Ottawa Real Estate</a></li>
        </ol>

    </section>

        <section class="xs-border-top xs-line-height-28 acc md-acc-open">
            <input class="acc-input" id="acc-also-like" type="checkbox">
            <span class="acc-icon"></span>
            <label class="acc-label xs-py4 xs-text-3 bold md-hide" for="acc-also-like">City Guide</label>
            <article class="acc-content">

                                    <section class="guide-prices container-xs xs-mb6 md-mt6">
                        <h2 class="xs-mb3">Home Prices in Ottawa</h2>
                                                <p>The asking price of homes for sale in Ottawa has decreased 14.91% since August last year, while the number of homes for sale has increased 79.92%. See more Ottawa Home Prices &amp; Values.</p>
                        <div class="clearfix xs-mb5 sm-py3">
                            <div class="col xs-col-12 sm-col-4 xs-border-bottom-lighter sm-border-bottom-none sm-border-right-lighter xs-p3 sm-py0 xs-text-center">
                                <div class="bold">House</div>
                                <div class="text-secondary xs-text-5">Median Asking Price</div>
                                <div class="xs-mt3 xs-mb3 xs-text-1 text-blue bold">$859K</div>
                                <a href="https://www.zolo.ca/ottawa-real-estate/houses" class="button button--mono xs-full-width">2,812 Houses</a>
                            </div>
                            <div class="col xs-col-12 sm-col-4 xs-border-bottom-lighter sm-border-bottom-none sm-border-right-lighter xs-p3 sm-py0 xs-text-center">
                                <div class="bold">Townhouse</div>
                                <div class="text-secondary xs-text-5">Median Asking Price</div>
                                <div class="xs-mt3 xs-mb3 xs-text-1 text-blue bold">$600K</div>
                                <a href="https://www.zolo.ca/ottawa-real-estate/townhouses" class="button button--mono xs-full-width">1,618 Townhouses</a>
                            </div>
                            <div class="col xs-col-12 sm-col-4 xs-p3 sm-py0 xs-text-center">
                                <div class="bold">Condo</div>
                                <div class="text-secondary xs-text-5">Median Asking Price</div>
                                <div class="xs-mt3 xs-mb3 xs-text-1 text-blue bold">$400K</div>
                                <a href="https://www.zolo.ca/ottawa-real-estate/condos" class="button button--mono xs-full-width">867 Condos</a>
                            </div>
                        </div>
                    </section>

                <section class="guide-details container-xs xs-mb6 md-mt6">
                                            <h2 class="xs-mb3">Find Real Estate &amp; MLS<sup>&reg;</sup> Listings in Ottawa</h2>

                    <p>Zolo has the most thorough, up-to-date set of Ottawa real estate listings. At the moment, Ottawa has 6,445 homes for sale, including 2,812 houses, 867 condos, and 1,618 townhouses on the market. </p><p>Ottawa is home to 147 unique neighbourhoods. With Zolo you'll be able to find the <a href="https://www.zolo.ca/ottawa-real-estate/neighbourhoods">most popular Ottawa neighbourhoods</a>, the highest-rated schools in the city, and evaluate nearby amenities. Check out the upcoming <a href="https://www.zolo.ca/ottawa-real-estate/open-houses">open houses in Ottawa</a> to tour homes in person. Narrow down your home search to filter by price, bedrooms, size or search by our <a href="https://www.zolo.ca/map-search">map of MLS&reg;</a> listings for Canada-wide real estate.</p><p>If you’re looking for rentals instead of homes for sale, Zolo has a comprehensive set of 1,299 <a href="https://www.zolo.ca/ottawa-real-estate/for-rent">Ottawa rental listings</a>, including 223 houses and 315 apartments for rent. Most of the population in the city 953</p><p>MLS&reg; Listing data for Ottawa is added every 15 minutes to provide you the most-up-to-date home listings. Get in touch with one of our Ottawa real estate agents to get started on landing your dream home today.</p>                </section>

                                    <section class="guide-overview container-xs xs-mb6 md-mt6">
                        <h2 class="xs-mb3">Ottawa City Guide</h2>

                        <div class="xs-mb5 sm-py3 xs-flex xs-flex-wrap">
                            <div class="xs-col-6 sm-col-4 sm-border-right-lighter xs-p3 sm-py0 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">883K</div>
                                <div class="text-secondary xs-text-5">Population</div>
                            </div>
                            <div class="xs-col-6 sm-col-4 sm-border-right-lighter xs-p3 sm-py0 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">8.8%</div>
                                <div class="text-secondary xs-text-5">Population Growth</div>
                            </div>
                            <div class="xs-col-12 sm-col-4 xs-p3 sm-py0 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">7%</div>
                                <div class="text-secondary xs-text-5">Unemployment</div>
                            </div>
                        </div>

                        <p>When it comes to a city that has it all, Ottawa, Ontario, might be it. Ottawa is the capital of Canada and the seat of government. It's a city that embraces the winter, basks in the summer, and finds a reason to celebrate just about every month of the year.</p>
<p>Located in southeastern Ontario, the city sits on the south bank of the Ottawa River and is home to just under one million residents. Ottawa is known for its beautiful seasons, plentiful outdoor space, music and arts scene, cultural diversity, and bi-lingual residents.</p>
<p>It's the 4th largest city in Canada, and its population has grown faster than the average Canadian growth rate. Ottawa's timezone is Eastern Daylight Time (GMT-4).</p>
<p>While it's known for its well-paying government jobs at Parliament Hill, Ottawa has also become a strong technology sector leader. According to <a href="https://www.statcan.gc.ca/en/start">Statistics Canada</a>, the city boasts the most well-educated population in Canada, with the most engineers, scientists, and PhDs per capita than any other city in the country. These above-average-pay industries contribute to Ottawa having the second highest family income among major Canadian cities at $102,000 annual median family income.</p>
<p>Ottawa is brimming with lifestyle and cultural amenities. Whether you want to skate the Rideau Canal &ndash; as many residents do each winter &ndash; or eat a picnic on Parliament Hill on Canada Day, there's an exciting communal event just about every weekend of the year. Then there are all the museums, monuments, and art galleries &ndash; the National Gallery of Canada, the Ottawa Art Gallery, the Ottawa Sports Hall of Fame, the Canadian War Museum, and the Canadian Museum of Nature &ndash; all located in Ottawa.</p>
<p>Since Ottawa doesn't sit on the shores of Lake Ontario, the summers are typically warmer, and the winters are colder than in other areas in Ontario. The city ranked third globally for being clean, boasting consistently high employment and low crime rates. It's also one of the most accessible cities to walk, bike, or take public transit to work. Ottawa also maintains 1,300 parks and green spaces, with some favorites including Confederation Park and Major's Hill Park. This all makes Ottawa a family-friendly city with safe neighborhoods and active lifestyles.</p>
<p>The city boasts many housing options for those looking to settle in Ottawa. There are new-build <a href="https://www.zolo.ca/ottawa-real-estate/condos">condos</a>, lofts, family-friendly <a href="https://www.zolo.ca/ottawa-real-estate/townhouses">townhomes</a>, and detached homes. For those looking for more character, you can find inner-city century homes, riverside cottages, and just about anything in-between.</p>                    </section>


                                    <section class="guide-neighbourhoods container-xs xs-mb6">
                        <h2 class="xs-mb3">Neighbourhoods</h2>
                        <p>As the 2nd-largest city in Ontario, Ottawa has over 80 neighbourhoods, including historic townships. The main urban area is a large segment of land that includes the former village of Rockcliffe Park, a high-income neighbourhood adjacent to the Ottawa River. Those looking for minimal commute time could consider looking for <a href="https://www.zolo.ca/ottawa-real-estate/centretown">homes in Centretown</a>. The sale price of homes in this community is higher, but everything is within walking distance. While there are a few detached homes with &quot;turn of the century&quot; character for sale, most residences for sale in Centretown are condos.</p>
<p>If you want a change from the big city and urban lifestyle, look for homes 20 minutes south in <a href="https://www.zolo.ca/ottawa-real-estate/kanata">Kanata</a>, the tech centre of Ottawa. The area started as seasonal cottages, and as Ottawa grew and people migrated to the suburbs, they converted the properties into four-season homes. You can buy a house and get the waterfront lifestyle, but you don't have to pay the downtown Ottawa prices.</p>
<p>Those interested in larger, newer homes should look in the Glebe area in Ottawa's east end. The nearby shopping complex of Lansdowne Park has become a big draw for residents. The larger homes in Glebe offer more bedrooms, bathrooms, and parking spaces for growing families.</p>
<p>Another good option for family homes is in Carleton Square. This area is near Carleton University, so families have an easy time renting out their mortgage-helper suites to university students.</p>
<p>If you have time, spend a weekend checking out the dozens of open houses in Ottawa. To help narrow your search, check out the hot list, which ranks all Ottawa neighbourhoods based on how many homes are for sale, the sale price and how long homes stay on the market. For more options, talk to your Ottawa Realtor.</p>
                        <table class="xs-text-center table-border xs-border-none">
                            <thead>
                            <tr>
                                <th class="xs-col-5">Neighbourhood <div class="normal xs-block sm-inline-block">(Top 5)</div></th>
                                <th class="xs-col-2 xs-text-center xs-hide sm-table-cell">Sold under 10d</th>
                                <th class="xs-col-2 xs-text-center xs-hide sm-table-cell">Sold above asking</th>
                                <th class="xs-col-2 xs-text-center">Average sale price</th>
                                <th class="xs-col-1 xs-text-center xs-hide sm-table-cell">Active listings</th>
                            </tr>
                            </thead>
                            <tbody class="priv">

          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">1</span><a href="https://www.zolo.ca/ottawa-real-estate/briargreen" class="xs-block xs-ml3 ">Briargreen</a>
            </td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top">$738K</td>
            <td class="xs-hide sm-table-cell">6</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">2</span><a href="https://www.zolo.ca/ottawa-real-estate/kenson-park" class="xs-block xs-ml3 ">Kenson Park</a>
            </td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top">$772K</td>
            <td class="xs-hide sm-table-cell">1</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">3</span><a href="https://www.zolo.ca/ottawa-real-estate/munster" class="xs-block xs-ml3 ">Munster</a>
            </td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top">$516K</td>
            <td class="xs-hide sm-table-cell">1</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">4</span><a href="https://www.zolo.ca/ottawa-real-estate/lincoln-heights" class="xs-block xs-ml3 ">Lincoln Heights</a>
            </td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top">$1.1M</td>
            <td class="xs-hide sm-table-cell">3</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">5</span><a href="https://www.zolo.ca/ottawa-real-estate/lakeview-park" class="xs-block xs-ml3 ">Lakeview Park</a>
            </td>
            <td class="xs-align-top xs-hide sm-table-cell">67%</td>
            <td class="xs-align-top xs-hide sm-table-cell">100%</td>
            <td class="xs-align-top">$792K</td>
            <td class="xs-hide sm-table-cell">3</td>
           </tr>                            </tbody>
                        </table>


                    </section>

                                    <section class="guide-cities container-xs xs-mb6">
                        <h2 class="xs-mb3">Nearby Cities</h2>
                        <p>Many buyers opt to purchase in a nearby city and commute to work. These buyers want larger homes and lots with commuter access to Ottawa's downtown core. Good options include Bedell, Gatineau, and <a href="https://www.zolo.ca/clarence-rockland-real-estate">Clarence-Rockland</a>. Look for neighbourhoods closer to the highway or commuter train stations, such as Kemptville or Du Ruisseau. Cities that require a bit longer of a commute, such as Merrickville, <a href="https://www.zolo.ca/smiths-falls-real-estate">Smith Falls</a>, and Clarence Creek, are other options as they offer more affordable detached homes.</p>
                        <table class="xs-text-center table-border xs-border-none">
                            <thead>
                            <tr>
                                <th class="xs-col-5">City <div class="normal xs-block sm-inline-block">(Top 5)</div></th>
                                                                <th class="xs-col-1 xs-text-center xs-hide sm-table-cell">Active listings</th>
                            </tr>
                            </thead>
                            <tbody class="priv">

          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">1</span><a href="https://www.zolo.ca/ottawa-real-estate" class="xs-block xs-ml3 ">Ottawa</a>
            </td><td class="xs-hide sm-table-cell">6389</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">2</span><a href="https://www.zolo.ca/clarence-rockland-real-estate" class="xs-block xs-ml3 ">Clarence-rockland</a>
            </td><td class="xs-hide sm-table-cell">186</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">3</span><a href="https://www.zolo.ca/russell-real-estate" class="xs-block xs-ml3 ">Russell</a>
            </td><td class="xs-hide sm-table-cell">134</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">4</span><a href="https://www.zolo.ca/chelsea-real-estate" class="xs-block xs-ml3 ">Chelsea</a>
            </td><td class="xs-hide sm-table-cell">4</td>
           </tr>
          <tr>
            <td class="xs-text-left xs-relative">
              <span class="table-rank text-muted">5</span><a href="https://www.zolo.ca/mayo-real-estate" class="xs-block xs-ml3 ">Mayo</a>
            </td><td class="xs-hide sm-table-cell">1</td>
           </tr>                            </tbody>
                        </table>


                    </section>

                                    <section class="guide-demographics container-xs xs-mb6">
                        <h2 class="xs-mb3">Demographics</h2>
                        <p>First look at the city's demographics if you're considering buying real estate in Ottawa. The information is collected by Census Canada and is meant to serve as an overview of a city's population. Information such as median age, average income, and ethnic background can give you a good idea of who lives in the city and if the area is right for you.</p>
                        <div class="sm-py3 xs-flex xs-flex-wrap">
                            <div class="xs-col-6 xs-border-right-lighter xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">33%</div>
                                <div class="text-secondary xs-text-5">Rent</div>
                            </div>
                            <div class="xs-col-6 xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">67%</div>
                                <div class="text-secondary xs-text-5">Own</div>
                            </div>
                            <div class="xs-col-6 xs-border-right-lighter xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">$953</div>
                                <div class="text-secondary xs-text-5">Monthly Rent (Median)</div>
                            </div>
                            <div class="xs-col-6 xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">$1,307</div>
                                <div class="text-secondary xs-text-5">Monthly Mortgage (Median)</div>
                            </div>
                        </div>
                        <div class="xs-mb5 sm-py3 xs-flex xs-flex-wrap xs-border-top-lighter">
                            <div class="xs-col-6 xs-border-right-lighter xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">230,640</div>
                                <div class="text-secondary xs-text-5">Total Families</div>
                            </div>
                            <div class="xs-col-6 xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">$79,634</div>
                                <div class="text-secondary xs-text-5">Family Income (Median)</div>
                            </div>
                            <div class="xs-col-6 xs-border-right-lighter xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">39</div>
                                <div class="text-secondary xs-text-5">Age (Median)</div>
                            </div>
                            <div class="xs-col-6 xs-px3 xs-my3 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">64%</div>
                                <div class="text-secondary xs-text-5">College Educated</div>
                            </div>
                        </div>
                    </section>

                                    <section class="guide-schools container-xs xs-mb6">
                        <h2 class="xs-mb3">Schools</h2>
                        <p>School locations can help families narrow down their Ottawa housing market search. Check out the city's top-rated elementary and secondary schools for families moving to Ottawa. Here are the top five schools in each category.</p>
                        <section class="schools-tables xs-grid sm-grid-cols-2 xs-gap-3 xs-mt5">
                                                            <section class="elementary-schools">
                                    <h4 class="bold xs-mb1">Elementary Schools</h4>
                                    <table class="md-width-full xs-text-5 sm-text-4 table-border xs-border-none">
                                        <col style="width:16.66667%">
                                        <col style="width:83.33333%">
                                        <tbody>
                                        <tr>
              <td class="xs-align-top text-green">8.8</td>
              <td class="xs-align-top">Terre-des-Jeunes</td>
            </tr><tr>
              <td class="xs-align-top text-green">8.2</td>
              <td class="xs-align-top">Elmdale</td>
            </tr><tr>
              <td class="xs-align-top text-green">8.2</td>
              <td class="xs-align-top">Hopewell Avenue</td>
            </tr><tr>
              <td class="xs-align-top text-green">8.1</td>
              <td class="xs-align-top">Woodroffe Avenue</td>
            </tr><tr>
              <td class="xs-align-top text-green">7.9</td>
              <td class="xs-align-top">Devonshire</td>
            </tr>                                        </tbody>
                                    </table>
                                </section>

                                                            <section class="secondary-schools">
                                    <h4 class="bold xs-mb1">Secondary Schools</h4>
                                    <table class="md-width-full xs-text-5 sm-text-4 table-border xs-border-none">
                                        <col style="width:16.66667%">
                                        <col style="width:83.33333%">
                                        <tbody>
                                        <tr>
              <td class="xs-align-top text-green">8.5</td>
              <td class="xs-align-top">Lisgar</td>
            </tr><tr>
              <td class="xs-align-top text-green">8.3</td>
              <td class="xs-align-top">Nepean</td>
            </tr><tr>
              <td class="xs-align-top text-green">7.6</td>
              <td class="xs-align-top">De La Salle</td>
            </tr><tr>
              <td class="xs-align-top text-green">7.5</td>
              <td class="xs-align-top">St Pius X</td>
            </tr><tr>
              <td class="xs-align-top text-green">7.5</td>
              <td class="xs-align-top">Canterbury</td>
            </tr>                                        </tbody>
                                    </table>
                                </section>

                        </section>
                    </section>

                                    <section class="guide-commuters container-xs xs-mb6">
                        <h2 class="xs-mb3">Commuters</h2>
                        <p>Ottawa's extensive local transportation system, <a href="https://www.octranspo.com/en/">OC Transpo</a>, operates bus services, train routes, and an upcoming light rail transit system. Ottawa also has rail lines to nearby cities and an international airport (YOW). Beyond commuting by car or public transportation, many residents opt to bike or walk to work.</p>
<p>To avoid the busy rush hour drive, you can either pick neighbourhoods closer to work or find various options for driving and taking transit.</p>
<p>To better understand how city residents in Ottawa commute, consider the data collected by Statistics Canada.</p>                        <div class="sm-pt3 xs-flex xs-flex-wrap">
                            <div class="xs-col-6 sm-col-3 sm-border-right-lighter xs-p3 sm-py0 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">67%</div>
                                <div class="text-secondary xs-text-5">Drive</div>
                            </div>
                            <div class="xs-col-6 sm-col-3 sm-border-right-lighter xs-p3 sm-py0 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">23%</div>
                                <div class="text-secondary xs-text-5">Transit</div>
                            </div>
                            <div class="xs-col-6 sm-col-3 sm-border-right-lighter xs-p3 sm-py0 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">3%</div>
                                <div class="text-secondary xs-text-5">Bike</div>
                            </div>
                            <div class="xs-col-6 sm-col-3 xs-p3 sm-py0 xs-text-center">
                                <div class="xs-mb1 xs-text-1 text-blue bold">7%</div>
                                <div class="text-secondary xs-text-5">Walk</div>
                            </div>
                        </div>
                    </section>

                                    <section class="guide-localscoop container-xs">
                        <h2 class="xs-mb3">Local Scoop</h2>
                        <p>The locals in Ottawa consistently find fun and interesting things to do, whether outdoor activities, enjoying the many festivals throughout the year, or eating beavertails (large, fried dough pancakes with sugar).</p>
<p>Ottawa has direct access to water throughout the city, including the Ottawa River, Rideau Canal, and Dow's Lake. Dow's Lake has a massive pavilion on the historic Rideau Canal with restaurants and scenic water views. There is also an active community for biking, walking, running, or rollerblading around Rideau Canal. Consider renting a canoe, kayak, or paddleboard on Dow's Lake.</p>
<p>On sunny afternoons beginning in May, many locals will congregate on Parliament Hill for group yoga. Free classes are every Wednesday, although impromptu groups pop up at other times.</p>
<p>For an enjoyable outdoor picnic, visit the Byward Market in Ottawa's downtown core. Not only can you pick up fresh fruit, beautiful flowers, or ready-made lunches, but there's often street entertainment.</p>
<p>One of the best free things to do in Ottawa is to tour the Parliament Buildings. The Parliament Buildings are home to Canada's federal government, steeped in history and beautiful architectural elements.</p>
<p>Several of the city's museums provide free admission during specific days of the week. You can get into the Canadian Museum of Nature, Canadian Museum of History, and the National Gallery of Canada for free on Thursday nights. It's also free to visit the Canadian Agricultural and Food Museum and the Canadian Aviation and Space Museum every day after 4 pm.</p>
<p>In June, consider spending the day at the Glow Fair. It's a block party where you can wear outrageous clothing, do yoga in the street, and dance to the sounds of local DJs.</p>
<p>Ottawa also has many festivals yearly, including the Canadian Tulip Festival in the spring and the Ottawa Bluesfest every summer. Winterlude is a winter festival held at Confederation Park downtown, with a snow and ice sculpture competition.</p>                    </section>

                <section class="xs-my6">
	<div class="container-xs">
		<h2 class="xs-mb3">FAQs About Ottawa Real Estate</h2>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-1" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-1"><h3 class="xs-text-4">What is the average home price in Ottawa, ON?</h3></label>
			<article class="acc-content">
				<p>According to current Ottawa MLS&copy; statistics, the average home listing price in Ottawa, ON is $877,000. Based on Ottawa housing inventory, the average home is listed on the market for 31 days and has a 97.8% selling to listing price ratio. </p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-2" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-2"><h3 class="xs-text-4">How much does a detached house cost in Ottawa?</h3></label>
			<article class="acc-content">
				<p>Based on current Ottawa MLS&copy; data, the average detached house in Ottawa, ON has a listing price of $1,033,000. In Ottawa, detached houses are on the market for 29 days on average.</p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-3" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-3"><h3 class="xs-text-4">How much does a condo cost in Ottawa?</h3></label>
			<article class="acc-content">
				<p>According to current Ottawa MLS&copy; data, the average condo in Ottawa, ON has a listing price of $479,000. In Ottawa, the average price for a 2-bedroom condo is $493,000 and the average price for a 1-bedroom condo is $397,000.</p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-4" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-4"><h3 class="xs-text-4">What are the most expensive neighbourhoods in Ottawa?</h3></label>
			<article class="acc-content">
				<p>In Ottawa, ON, the most expensive neighbourhoods to buy a home include Kinburn, Rothwell Heights, Experimental Farm and Mooneys Bay. Kinburn is the most expensive neighbourhood in Ottawa with an average home price of $835,000.</p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-5" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-5"><h3 class="xs-text-4">What are the cheapest neighbourhoods to buy a home in Ottawa?</h3></label>
			<article class="acc-content">
				<p>The cheapest neighbourhoods to buy a home in Ottawa include Heron Gate, Carleton Square, Viscount Alexander Park and Britannia Heights. Heron Gate is the most affordable neighbourhood in Ottawa with an average home price of $248,000.</p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-6" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-6"><h3 class="xs-text-4">What are nearby cities to Ottawa, ON?</h3></label>
			<article class="acc-content">
				<p>The closest cities to Ottawa, ON include Kanata, Stittsville, Russell. Out of the nearby Ottawa cities, Stittsville has the highest average home price of $959,000 and Russell has the most affordable average home price of $751,000.</p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-7" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-7"><h3 class="xs-text-4">How much does a townhouse cost in Ottawa?</h3></label>
			<article class="acc-content">
				<p>The average townhouse in Ottawa has an average listing price of $687,000, which represents a -18.7% change in price from last year. There are currently 1608 townhouses for sale in Ottawa, ON.</p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-8" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-8"><h3 class="xs-text-4">What&#039;s the average income of a Ottawa, ON resident?</h3></label>
			<article class="acc-content">
				<p>According to Statistics Canada Census data, the median total household income in Ottawa is $79,634 per year, which is above the national median income of $67,000 per year.</p>
			</article>
		</section>
				<section class="xs-border-top-lighter acc">
			<input class="acc-input" id="search-faq-9" type="checkbox">
			<span class="acc-icon xs-t3"></span>
			<label class="acc-label xs-py3 xs-pr4" for="search-faq-9"><h3 class="xs-text-4">How many people rent versus buy real estate in Ottawa?</h3></label>
			<article class="acc-content">
				<p>In Ottawa, ON, fewer people rent their homes than own at 32.7% rent versus 67.3% own. The median monthly rent price is $2,500 per month versus the median monthly mortgage of $0 per month.</p>
			</article>
		</section>
			</div>
</section>
            </article>
        </section>


<section class="guide-related xs-border-top acc md-acc-open  md-border-top-none ">
    <input class="acc-input" id="acc-links" type="checkbox">
    <span class="acc-icon"></span>
    <label class="acc-label xs-py4 xs-text-3 bold md-hide" for="acc-links">You Might Also Like&hellip;</label>
    <article class="acc-content ">
                <div class="container xs-pb3 md-py5  md-border-top ">
            <div class="sm-flex sm-flex-wrap  xs-flex-justify-center ">
                                                        <nav class="xs-col-12 sm-col-6 md-col-3 sm-pr3 xs-mb3 md-mb0">
                        <h4 class="xs-text-3 bold xs-mb1">Popular Searches</h4>
                        <ul class="list-unstyled xs-line-height-28 sm-line-height-inherit">
                            <li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ontario-real-estate" class="text-primary hover:decoration-underline">Ontario Real Estate</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/open-houses" class="text-primary hover:decoration-underline">Ottawa Open Houses</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/houses" class="text-primary hover:decoration-underline">Ottawa Houses</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/townhouses" class="text-primary hover:decoration-underline">Ottawa Townhouses</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/condos" class="text-primary hover:decoration-underline">Ottawa Condos</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/houses-for-rent" class="text-primary hover:decoration-underline">Ottawa Houses For Rent</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/townhouses-for-rent" class="text-primary hover:decoration-underline">Ottawa Townhouses For Rent</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/apartments-for-rent" class="text-primary hover:decoration-underline">Ottawa Apartments For Rent</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/studio-bedroom-apartments-for-rent" class="text-primary hover:decoration-underline">Ottawa Studio Apartments For Rent</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/1-bedroom-apartments-for-rent" class="text-primary hover:decoration-underline">Ottawa 1 Bedroom Apartments For Rent</a>
                                </li><li class="xs-mb1 truncate">
                                    <a href="https://www.zolo.ca/ottawa-real-estate/2-bedroom-apartments-for-rent" class="text-primary hover:decoration-underline">Ottawa 2 Bedroom Apartments For Rent</a>
                                </li>                        </ul>
                    </nav>

                                <nav class="xs-col-12 sm-col-6 md-col-3 sm-pr3 xs-mb3 md-mb0">
                    <h4 class="xs-text-3 bold xs-mb1">Neighborhoods</h4>
                    <ul class="list-unstyled xs-line-height-28 sm-line-height-inherit">
                        <li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/sandy-hill" class="text-primary hover:decoration-underline">Sandy Hill </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/riverside-south" class="text-primary hover:decoration-underline">Riverside South </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/vanier" class="text-primary hover:decoration-underline">Vanier </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/ottawa-centre" class="text-primary hover:decoration-underline">Ottawa Centre </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/lower-town" class="text-primary hover:decoration-underline">Lower Town </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/hintonburg" class="text-primary hover:decoration-underline">Hintonburg </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/west-centre-town" class="text-primary hover:decoration-underline">West Centre Town </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/carlington" class="text-primary hover:decoration-underline">Carlington </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/byward-market" class="text-primary hover:decoration-underline">Byward Market </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate/alta-vista" class="text-primary hover:decoration-underline">Alta Vista </a>
                            </li>                    </ul>
                </nav>
                                                <nav class="xs-col-12 sm-col-6 md-col-3 sm-pr3 xs-mb3 md-mb0">
                    <h4 class="xs-text-3 bold xs-mb1">Nearby Cities</h4>
                    <ul class="list-unstyled xs-line-height-28 sm-line-height-inherit">
                        <li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/manotick-real-estate" class="text-primary hover:decoration-underline">Manotick </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/ottawa-real-estate" class="text-primary hover:decoration-underline">Ottawa </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/carp-real-estate" class="text-primary hover:decoration-underline">Carp </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/oxford-mills-real-estate" class="text-primary hover:decoration-underline">Oxford Mills </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/woodlawn-real-estate" class="text-primary hover:decoration-underline">Woodlawn </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/carleton-place-real-estate" class="text-primary hover:decoration-underline">Carleton Place </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/russell-real-estate" class="text-primary hover:decoration-underline">Russell </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/kinburn-real-estate" class="text-primary hover:decoration-underline">Kinburn </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/merrickville-real-estate" class="text-primary hover:decoration-underline">Merrickville </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/smiths-falls-real-estate" class="text-primary hover:decoration-underline">Smiths Falls </a>
                            </li>                    </ul>
                </nav>
                                                <nav class="xs-col-12 sm-col-6 md-col-3 sm-pr3 xs-mb3 md-mb0">
                    <h4 class="xs-text-3 bold xs-mb1">Popular Cities</h4>
                    <ul class="list-unstyled xs-line-height-28 sm-line-height-inherit">
                        <li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/abbotsford-real-estate" class="text-primary hover:decoration-underline">Abbotsford </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/kitchener-real-estate" class="text-primary hover:decoration-underline">Kitchener </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/barrie-real-estate" class="text-primary hover:decoration-underline">Barrie </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/windsor-real-estate" class="text-primary hover:decoration-underline">Windsor </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/st-catharines-real-estate" class="text-primary hover:decoration-underline">St. Catharines </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/chilliwack-real-estate" class="text-primary hover:decoration-underline">Chilliwack </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/kamloops-real-estate" class="text-primary hover:decoration-underline">Kamloops </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/kawartha-lakes-real-estate" class="text-primary hover:decoration-underline">Kawartha Lakes </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/north-vancouver-real-estate" class="text-primary hover:decoration-underline">North Vancouver </a>
                            </li><li class="xs-mb1 truncate">
                                <a href="https://www.zolo.ca/maple-ridge-real-estate" class="text-primary hover:decoration-underline">Maple Ridge </a>
                            </li>                    </ul>
                </nav>
                                            </div>
        </div>
    </article>
</section>


<div class="xs-hide js-search-city">ottawa</div>
<div class="xs-hide js-search-neighborhood"></div>
<div class="xs-hide js-search-neighborhood"></div>

    <section class="xs-border-top md-border-top-none acc md-acc-open">
        <input class="acc-input" id="acc-disclaimer" type="checkbox">
        <span class="acc-icon"></span>
        <label class="acc-label xs-py4 xs-text-3 bold md-hide" for="acc-disclaimer">Disclaimer</label>
        <article class="acc-content">
            <div class="container flex xs-flex-column sm-flex-row xs-pb3 md-py5 md-border-top">
                <div>
                    The listing data is provided under copyright by the Toronto Regional Real Estate Board (TRREB). The information provided herein must only be used by consumers that have a bona fide interest in the purchase, sale or lease of real estate and may not be used for any commercial purpose or any other purpose. The data is deemed reliable but is not guaranteed accurate by the Toronto Regional Real Estate Board nor Zolo. The information provided on this page, including the Affordability, Afford Score™, and Affordability Coach, are provided for informational purposes only and should not be used or construed as financial or investment advice by any individual. No representations or warranties, express or implied, are made by Zolo or its affiliates as to the accuracy or completeness of the information on this page.                </div>
            </div>
        </article>
    </section>

<div class="loading-overlay xs-text-center xs-absolute xs-t0 xs-r0 xs-b0 xs-l0 xs-z2 xs-pt5">
    <div class="fill-white xs-inline-flex xs-flex-align-center xs-flex-justify-center xs-h-60 xs-w-60 circle shadow-2">
        <svg viewBox="0 0 24 24" height="32" width="32" class="fill-current text-blue" aria-hidden="true"><path d="M19 8l-4 4h3a6 6 0 01-8.8 5.3l-1.46 1.46A8 8 0 0020 12h3l-4-4zM6 12a6 6 0 018.8-5.3l1.46-1.46A8 8 0 004 12H1l4 4 4-4H6z"/></svg>
    </div>
</div>


    <!-- modal launcher-->
    <div id="sortModal" class="modal hide overflow-y-auto pointer-events-none" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <!-- modal wrapper -->
        <div class="modal-content-wrapper xs-full-height xs-flex xs-flex-align-end sm-flex-align-center xs-flex-justify-center xs-relative">
            <!-- modal content -->
            <div class="modal-content fill-white xs-rounded-md shadow-3 xs-full-width xs-max-h-full sm-max-w-lg xs-overflow-hidden xs-relative xs-flex xs-flex-column pointer-events-auto">
                <header class="gut xs-flex xs-flex-align-center xs-flex-shrink-0 xs-h-64 xs-z2">
                    <div class="xs-flex xs-flex-align-center">
                        <button class="button button--menu xs-border-none xs-inline-flex xs-flex-shrink-0 xs-p1 xs-line-height-1 circle hover:fill-grey-bg-2 xs-absolute xs-t2 xs-l2" data-dismiss="modal" aria-hidden="true">
                            <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current">
                                <path class="md-hide" d="M10 4.93L2.93 12 10 19.07l1.5-1.5L6.93 13H21v-2H6.93l4.57-4.57-1.5-1.5z"/>
                                <path class="xs-hide md-block" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    </div>
                </header>
                <div class="gut xs-mt1 xs-pb5 overflow-y-auto">
                    <div class="xs-text-1 bold xs-mb3">Sort Results</div>
                    <nav class="xs-flex xs-flex-column xs-gap-5">
                        <section class="xs-flex xs-flex-wrap xs-gap-1">
                            <a class="button button--white-black text-white fill-primary xs-border-transparent" href="https://www.zolo.ca/index.php?attribute_terms=&has_photos=&days_on_zolo=0&ptype_condo=0&ptype_townhouse=0&ptype_house=0&stype=&min_price=0&max_price=0&min_beds=0&min_baths=0&min_sqft=0&openhouse_search=0&filter=1&sarea=Ottawa&s_r=1&search_order=0" rel="nofollow" >Recommended</a>
                        </section>
                        <section class="xs-flex xs-flex-wrap xs-gap-1">
                            <div class="caps text-secondary bold xs-text-6 xs-mb1 xs-full-width">
                                Days on Site
                            </div>
                            <a class="button button--white-black " href="https://www.zolo.ca/index.php?attribute_terms=&has_photos=&days_on_zolo=0&ptype_condo=0&ptype_townhouse=0&ptype_house=0&stype=&min_price=0&max_price=0&min_beds=0&min_baths=0&min_sqft=0&openhouse_search=0&filter=1&sarea=Ottawa&s_r=1&search_order=3" rel="nofollow" >Most Recent</a>
                            <a class="button button--white-black " href="https://www.zolo.ca/index.php?attribute_terms=&has_photos=&days_on_zolo=0&ptype_condo=0&ptype_townhouse=0&ptype_house=0&stype=&min_price=0&max_price=0&min_beds=0&min_baths=0&min_sqft=0&openhouse_search=0&filter=1&sarea=Ottawa&s_r=1&search_order=4" rel="nofollow" >Oldest</a>
						</section>
                        <section class="xs-flex xs-flex-wrap xs-gap-1">
                            <div class="caps text-secondary bold xs-text-6 xs-mb1 xs-full-width">
                                Price
                            </div>
                            <a class="button button--white-black " href="https://www.zolo.ca/index.php?attribute_terms=&has_photos=&days_on_zolo=0&ptype_condo=0&ptype_townhouse=0&ptype_house=0&stype=&min_price=0&max_price=0&min_beds=0&min_baths=0&min_sqft=0&openhouse_search=0&filter=1&sarea=Ottawa&s_r=1&search_order=1" rel="nofollow" >Most Expensive</a>
                            <a class="button button--white-black "  href="https://www.zolo.ca/index.php?attribute_terms=&has_photos=&days_on_zolo=0&ptype_condo=0&ptype_townhouse=0&ptype_house=0&stype=&min_price=0&max_price=0&min_beds=0&min_baths=0&min_sqft=0&openhouse_search=0&filter=1&sarea=Ottawa&s_r=1&search_order=2" rel="nofollow" >Least Expensive</a>
                        </section>
                        <section class="xs-flex xs-flex-wrap xs-gap-1">
                            <div class="caps text-secondary bold xs-text-6 xs-mb1 xs-full-width">
                                Bedrooms
                            </div>
                            <a class="button button--white-black " href="https://www.zolo.ca/index.php?attribute_terms=&has_photos=&days_on_zolo=0&ptype_condo=0&ptype_townhouse=0&ptype_house=0&stype=&min_price=0&max_price=0&min_beds=0&min_baths=0&min_sqft=0&openhouse_search=0&filter=1&sarea=Ottawa&s_r=1&search_order=5" rel="nofollow" >Most Bedrooms</a>
                        </section>
                    </nav>
                </div>

            </div>
        </div>
    </div>
</main>
    <footer class="xs-relative gut">
        <div class="footer-nav-boxes xs-border-top xs-flex xs-flex-column md-flex-row md-flex-wrap container md-pt5 md-pb5 acc md-acc-open">
            <input class="acc-input" id="acc-footer" type="checkbox">
            <span class="acc-icon"></span>
            <label class="acc-label xs-py4 xs-text-3 text-primary bold md-hide" for="acc-footer">More</label>
            <section class="md-col-8 md-pr5 acc-content">
                <nav class="xs-mb3 sm-line-height-inherit bold">
                    <a class="text-primary hover:decoration-underline xs-inline-block xs-mr2 xs-mb1" href="https://www.zolo.ca/about.php">Zolo Realty, Brokerage</a>
                    <a class="text-primary hover:decoration-underline xs-inline-block xs-mr2 xs-mb1" href="https://www.zolo.ca/contact_us.php">Contact</a>
                    <a class="text-primary hover:decoration-underline xs-inline-block xs-mr2 xs-mb1 nowrap" href="https://www.zolo.ca/legal">Privacy &amp; Terms</a>
                    <a class="text-primary hover:decoration-underline xs-inline-block xs-mr2 xs-mb1" href="https://www.zolo.ca/sitemap">Sitemap</a>
                    <a class="text-primary hover:decoration-underline xs-inline-block xs-mr2 xs-mb1" href="https://www.zolo.ca/mobile">Mobile</a>
                    <input class="acc-input" id="acc-offices" type="checkbox">
                    <label class="acc-label text-primary hover:decoration-underline xs-inline-block xs-mb1 nowrap" for="acc-offices">Offices +</label>

                    <section class="md-text-5 acc-content normal">
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty, Brokerage,</span>
                            <span class="xs-block sm-inline-block">1900-5700 Yonge Street,</span>
                            <span class="xs-block sm-inline-block">Toronto, ON, M2N 4K2,</span>
                            <span class="xs-block sm-inline-block">************</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty, Conveyancing Department,</span>
                            <span class="xs-block sm-inline-block">1900-5700 Yonge Street,</span>
                            <span class="xs-block sm-inline-block">Toronto, ON, M2N 4K2,</span>
                            <span class="xs-block sm-inline-block">************, 416.981.3248 (Fax)</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (London, ON),</span>
                            <span class="xs-block sm-inline-block">Tower B, 6th Floor, 380 Wellington Street,</span>
                            <span class="xs-block sm-inline-block">London, ON, N6A 5B5,</span>
                            <span class="xs-block sm-inline-block">************</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Ottawa, ON),</span>
                            <span class="xs-block sm-inline-block">11th Floor, 343 Preston Street,</span>
                            <span class="xs-block sm-inline-block">Ottawa, ON, K1S 1N4,</span>
                            <span class="xs-block sm-inline-block">613.707.6328</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Kitchener-Waterloo, ON),</span>
                            <span class="xs-block sm-inline-block">1st Floor, Unit 4-180 Northfield Drive West,</span>
                            <span class="xs-block sm-inline-block">Waterloo, ON, N2L 0C7,</span>
                            <span class="xs-block sm-inline-block">226.779.3797</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Windsor, ON),</span>
                            <span class="xs-block sm-inline-block">2557 Dougall Avenue Unit: 6,</span>
                            <span class="xs-block sm-inline-block">Windsor, ON, N8X 1T5,</span>
                            <span class="xs-block sm-inline-block">226.779.3797</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Peterborough/Kawartha Lakes, ON),</span>
                            <span class="xs-block sm-inline-block">18 Bridge Street West,</span>
                            <span class="xs-block sm-inline-block">Campbellford, ON, K0L 1L0,</span>
                            <span class="xs-block sm-inline-block">************</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Vancouver, BC),</span>
                            <span class="xs-block sm-inline-block">Unit 300-1090 Homer Street,</span>
                            <span class="xs-block sm-inline-block">Vancouver, BC, V6B 2W9,</span>
                            <span class="xs-block sm-inline-block">604.239.1671</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Victoria, BC),</span>
                            <span class="xs-block sm-inline-block">Unit 301-1321 Blanshard Street,</span>
                            <span class="xs-block sm-inline-block">Victoria, BC, V8W 0B6,</span>
                            <span class="xs-block sm-inline-block">778.654.1127</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Kelowna, BC),</span>
                            <span class="xs-block sm-inline-block">Unit 1100-1631 Dickson Avenue,</span>
                            <span class="xs-block sm-inline-block">Kelowna, BC, V1Y 0B5,</span>
                            <span class="xs-block sm-inline-block">778.762.0469</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Penticton, BC),</span>
                            <span class="xs-block sm-inline-block">129 West Nanaimo Avenue,</span>
                            <span class="xs-block sm-inline-block">Penticton, BC, V2A 1N2,</span>
                            <span class="xs-block sm-inline-block">778.762.0469</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Calgary, AB),</span>
                            <span class="xs-block sm-inline-block">30th Floor, 421 7th Avenue S.W.,</span>
                            <span class="xs-block sm-inline-block">Calgary, AB, T2P 4K9,</span>
                            <span class="xs-block sm-inline-block">587.409.2396</span>
                        </address>
                        <address class="xs-mb3 sm-mb1 xs-mt3">
                            <span class="xs-block sm-inline-block">Zolo Realty (Regina, SK),</span>
                            <span class="xs-block sm-inline-block">7th Floor, 2010 11th Avenue,</span>
                            <span class="xs-block sm-inline-block">Regina, SK, S4P 0J3,</span>
                            <span class="xs-block sm-inline-block">1.888.230.1595</span>
                        </address>
                    </section>
                </nav>

                <section class="xs-mt3 xs-pb3 md-pb0 md-text-5 sm-flex sm-flex-wrap sm-flex-align-center">
                    <div class="sm-mr3 xs-mb3 sm-mb0">
                        <a href="https://www.facebook.com/zolocanada" aria-label="Zolo on Facebook" class="xs-inline-flex button button--white-black xs-p1 xs-line-height-1 circle xs-mr1">
                            <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current"><path d="M17.52 9H14V7c0-1.03.08-1.68 1.56-1.68h1.87V2.14c-.9-.1-1.82-.14-2.74-.14C11.98 2 10 3.66 10 6.7V9H7v4h3v9h4v-9h3.07l.45-4z"/></svg>
                        </a>
                        <a href="https://www.twitter.com/zolocanada" aria-label="Zolo on Twitter" class="xs-inline-flex button button--white-black xs-p1 xs-line-height-1 circle">
                            <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current"><path d="M23.6 5c-.8.3-1.7.6-2.6.7 1-.6 1.7-1.5 2-2.6-.9.5-1.9 1-3 1.1a4.7 4.7 0 00-7.9 4.3c-3.9-.2-7.3-2-9.6-4.9A4.7 4.7 0 004 9.8c-.8 0-1.5-.2-2.2-.6v.1c0 2.3 1.7 4.1 3.8 4.6a4.7 4.7 0 01-2.1 0 4.7 4.7 0 004.3 3.3 9.3 9.3 0 01-6.9 2c2.1 1.2 4.5 2 7.2 2A13.2 13.2 0 0021.3 7.4c1-.7 1.7-1.5 2.3-2.5z"/></svg>
                        </a>
                    </div>
                    <div>
                        <div class="xs-mb05">
                            <a class="text-primary hover:decoration-underline xs-inline-block" href="/sitemap/latest">Newest Listing</a> <a class="text-primary hover:decoration-underline"  href="https://www.zolo.ca/innisfil-real-estate/8-carmans-cove">8 Carmans Cove N/a, Innisfil, ON</a>                        </div>
                        <div>Updated: <time>August 9th 2025 8:20 am</time> CT</div>                    </div>
                    <div class="xs-mt3 xs-text-6">
                        <p>
                             Zolo Realty, Brokerage, the Zolo Web App, and the Zolo App are operated under license from Questrade, Inc.
Zolo Ventures Ltd., its subsidiaries, Community Trust Company ("CTC"), Flexiti Financial Inc. and Questrade, Inc. are members of the Questrade Group of Companies. Questrade Group of Companies means Questrade Financial Group Inc. and its affiliates that provide deposit, investment, loan, securities, mortgages, real estate and other products or services.<br>
                        </p>
                        <p class="xs-mb0">
                        The REALTOR® trademark is controlled by The Canadian Real Estate Association (CREA) and identifies real estate professionals who are members of CREA. The trademarks MLS®, Multiple Listing Service® and the associated logos identify professional services rendered by REALTOR® members of CREA to effect the purchase, sale and lease of real estate as part of a cooperative selling system.
                            <span>
                                0.29                                index                            </span>
                        </p>
                    </div>
                </section>
            </section>

            <section class="md-col-4 xs-border-top sm-border-top md-border-top-none xs-mb6 md-mb0">

  <div class="footer-recruit xs-py6 md-py0 xs-text-center">
    <div class="xs-text-1 text-primary xs-mb3 bold">Are You a REALTOR<sup>&reg;</sup>?</div>
    <a class="button button--large fill-primary hover:fill-secondary xs-text-3 xs-px5 bold pill" href="https://www.zolo.ca/jobs">Join Zolo Today!</a>
  </div>
            </section>
        </div>
        <div class="xs-flex xs-h-8 gut-neg">
            <div class="fill-blue xs-flex-grow-1"></div>
            <div class="fill-green xs-flex-grow-1"></div>
            <div class="fill-yellow xs-flex-grow-1"></div>
            <div class="fill-orange xs-flex-grow-1"></div>
        </div>
    </footer>




<div class="drawer drawer-top drawer-location transition-none fill-white md-hide print-hide" id="drawer-location">
    <header class="header drawer-header gut xs-flex xs-flex-align-center md-hide xs-z4 xs-relative xs-shadow-bottom">
        <button class="button button--menu xs-border-none circle xs-inline-flex xs-flex-shrink-0 xs-p1 xs-mr1 drawer-location-close" style="margin-left: -.5rem;" aria-label="close">
            <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current" aria-hidden="true"><path d="M10 4.93L2.93 12 10 19.07l1.5-1.5L6.93 13H21v-2H6.93l4.57-4.57-1.5-1.5z"/></svg>
        </button>
        <div class="filter-location-input xs-full-width xs-flex xs-relative">
                                <label for="mobile_sarea" class="sr-only">Search by location, address, or MLS number</label>
                    <input autocomplete="off" class="text-input xs-full-width xs-pr4 fill-grey-bg-2 xs-border-none rounded no-focus xs-h-40" type="text" placeholder="City, Address, MLS&reg;#" name="mobile_sarea" id="mobile_sarea" value="Ottawa">
                                            <button id="clear-sarea-input-field" class="button button--menu xs-flex xs-flex-align-center xs-flex-justify-center xs-border-none rounded xs-p1 xs-absolute xs-r0 xs-t0 xs-full-height" aria-label="Clear search input">
                <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current" aria-hidden="true"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
            </button>
        </div>
    </header>
    <div class="drawer-body" id="mobile-typeahead-dropdown-menu">
        <ul id="mobile-other-searches-menu" class="typeahead dropdown-menu xs-full-width list-unstyled xs-border-none xs-p0 xs-py3">
                            <li><div class="xs-flex xs-flex-align-center xs-text-6 text-muted heavy caps xs-px3 xs-py1">Nearby locations</div></li>
                                        <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/manotick-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Manotick </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/ottawa-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Ottawa </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/carp-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Carp </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/oxford-mills-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Oxford Mills </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/woodlawn-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Woodlawn </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/carleton-place-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Carleton Place </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/russell-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Russell </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/kinburn-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Kinburn </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/merrickville-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Merrickville </div>
                            </a>
                        </li>
                                                <li>
                            <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-nearby-cities-search" href="https://www.zolo.ca/smiths-falls-real-estate" rel="nofollow">
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M12 2a7 7 0 00-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 00-7-7zm0 9.5a2.5 2.5 0 110-5 2.5 2.5 0 010 5z"/></svg>
                                <div class="truncate xs-full-width">Smiths Falls </div>
                            </a>
                        </li>
                                            <li class="xs-mt2"><div class="xs-flex xs-flex-align-center xs-text-6 text-muted heavy caps xs-px3 xs-py1">Popular searches</div></li>
                                                <li>
                                <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-popular-search-search" href="https://www.zolo.ca/ottawa-real-estate/houses" rel="nofollow">
                                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                                    <div class="truncate xs-full-width">Ottawa Houses for Sale</div>
                                </a>
                            </li>
                                                        <li>
                                <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-popular-search-search" href="https://www.zolo.ca/ottawa-real-estate/townhouses" rel="nofollow">
                                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                                    <div class="truncate xs-full-width">Ottawa Townhouses for Sale</div>
                                </a>
                            </li>
                                                        <li>
                                <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-popular-search-search" href="https://www.zolo.ca/ottawa-real-estate/condos" rel="nofollow">
                                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                                    <div class="truncate xs-full-width">Ottawa Condos for Sale</div>
                                </a>
                            </li>
                                                        <li>
                                <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-popular-search-search" href="https://www.zolo.ca/ottawa-real-estate/for-rent" rel="nofollow">
                                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                                    <div class="truncate xs-full-width">Ottawa Rentals</div>
                                </a>
                            </li>
                                                        <li>
                                <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-popular-search-search" href="https://www.zolo.ca/ottawa-real-estate/houses-for-rent" rel="nofollow">
                                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                                    <div class="truncate xs-full-width">Ottawa Houses for Rent</div>
                                </a>
                            </li>
                                                        <li>
                                <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-popular-search-search" href="https://www.zolo.ca/ottawa-real-estate/townhouses-for-rent" rel="nofollow">
                                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                                    <div class="truncate xs-full-width">Ottawa Townhouses for Rent</div>
                                </a>
                            </li>
                                                        <li>
                                <a class="button button--menu rounded-none xs-px3 xs-text-4 xs-full-width xs-text-left xs-flex xs-flex-align-center normal mobile-popular-search-search" href="https://www.zolo.ca/ottawa-real-estate/apartments-for-rent" rel="nofollow">
                                    <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-secondary fill-grey-bg-3 circle xs-p05 xs-mr3 xs-flex-shrink-0"><path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 10-.7.7l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0a4.5 4.5 0 11-.01-8.99A4.5 4.5 0 019.5 14z"/></svg>
                                    <div class="truncate xs-full-width">Ottawa Apartments for Rent</div>
                                </a>
                            </li>
                                    </ul>
    </div>
</div>


    <!-- modal launcher-->
    <div id="savesearchModal" class="modal hide overflow-y-auto pointer-events-none" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="z-index: 6002;">
        <!-- modal wrapper -->
        <div class="modal-content-wrapper xs-full-height xs-flex xs-flex-align-end sm-flex-align-center xs-flex-justify-center xs-relative">
            <!-- modal content -->
            <div class="modal-content fill-white xs-rounded-md shadow-3 xs-full-width xs-max-h-full sm-max-w-lg xs-overflow-hidden xs-relative xs-flex xs-flex-column pointer-events-auto">

                <header class="gut xs-flex xs-flex-align-center xs-flex-shrink-0 xs-h-64 xs-z2">
                    <div class="xs-flex xs-flex-align-center">
                        <button class="button button--menu xs-border-none xs-inline-flex xs-flex-shrink-0 xs-p1 xs-line-height-1 circle hover:fill-grey-bg-2 xs-absolute xs-t2 xs-l2" data-dismiss="modal" aria-hidden="true">
                            <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current">
                                <path class="md-hide" d="M10 4.93L2.93 12 10 19.07l1.5-1.5L6.93 13H21v-2H6.93l4.57-4.57-1.5-1.5z"/>
                                <path class="xs-hide md-block" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    </div>
                </header>

                <div class="gut xs-mt1 xs-mb3" style="min-height:10rem;">
                    <div class="xs-text-1 bold xs-mb3">Save this Search</div>
                    <p><span id="save-search-promo-content"></span></p>
                </div>
                <div class="xs-flex-shrink-0 gut xs-py2 xs-flex xs-flex-items-center xs-border-top">
                                            <button class="button button--large fill-primary hover:fill-secondary" data-dismiss="modal" data-toggle="modal" data-target="#signupModaltop">Sign In to Save Search</button>
                                    </div>

            </div>
        </div>
    </div>


<div id="listingModal" class="modal hide overflow-y-auto pointer-events-none" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="z-index: 6002;"></div>


<!-- modal launcher-->
<div id="signupModaltop" class="sign-up modal hide overflow-y-auto pointer-events-none" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="z-index: 6002;">
    <!-- modal wrapper -->
    <div class="modal-content-wrapper xs-full-height xs-flex xs-flex-align-end sm-flex-align-center xs-flex-justify-center xs-relative">
        <!-- modal content -->
        <div class="modal-content fill-white xs-rounded-md shadow-3 xs-max-h-full xs-full-width sm-max-w-lg xs-overflow-hidden xs-relative xs-flex xs-flex-column pointer-events-auto">

            <header class="gut xs-flex xs-flex-align-center xs-flex-shrink-0 xs-h-64 xs-z2">
                <div class="xs-flex xs-flex-align-center">
                    <button class="button button--menu xs-border-none xs-inline-flex xs-flex-shrink-0 xs-p1 xs-line-height-1 circle hover:fill-grey-bg-2 xs-absolute xs-t2 xs-l2" data-dismiss="modal" aria-hidden="true">
                        <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current">
                            <path class="md-hide" d="M10 4.93L2.93 12 10 19.07l1.5-1.5L6.93 13H21v-2H6.93l4.57-4.57-1.5-1.5z"/>
                            <path class="xs-hide md-block" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            </header>


                <section class="gut xs-pb3 md-pb5 overflow-y-auto  xs-hide  js-signup-page-3">
                    <form class="form js-signup-form js-signup-form-3" name="signup_form" id="signup-form-3" method="get">
                        <h3 class="xs-text-1 heavy xs-mb3 heavy xs-mb3 xs-mr4">Verified Email Required</h3>
                        <p>Real Estate boards require a verified email prior to accessing full listing data including sold prices.</p>
                        <p class="xs-mb0 bold">Zolo has sent you an email.</p>
                        <p class="text-green bold xs-mb5">Check your inbox to finish creating your full account.</p>
                        <div class="xs-flex xs-flex-column xs-gap-2">
                            <button type="submit" class="button button--large fill-primary hover:fill-secondary xs-text-3 xs-full-width xs-flex xs-flex-justify-center bold shadow-2">
                                I have verified already
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-green xs-ml1 xs-flex-shrink-0"><path d="m23 12-2.44-2.78.34-3.68-3.61-.82-1.89-3.18L12 3 8.6 1.54 6.71 4.72l-3.61.81.34 3.68L1 12l2.44 2.78-.34 3.69 3.61.82 1.89 3.18L12 21l3.4 1.46 1.89-3.18 3.61-.82-.34-3.68L23 12m-13 5-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8Z"/></svg>
                            </button>
                            <div id="resend_confirmation_text"><a href="#" class="button button--large button--white-black xs-text-3 xs-full-width xs-flex xs-flex-justify-center" id="resend_confirmation_link">Re-send Verification Email</a></div>
                        </div>
                    </form>
                </section>



                <section class="gut xs-pb3 md-pb5 overflow-y-auto" id="account_signup_wrapper">
                    <section class=" xs-block  js-signup-page-1">
                        <section class="why-register xs-mt1 xs-mb3">
                            <h3 class="xs-text-1 heavy xs-mb3">Register <span class="xs-text-2 normal">or</span> Sign In</h3>
                            <h4 class="xs-text-3 bold xs-mb3">Join 10 million+ Canadians searching for homes on Zolo each month.</h4>
                            <ul class="signup-value-prop-list xs-pl2">
                                <li class="xs-mb05">Faster listings than REALTOR.ca®</li>
                                <li class="xs-mb05">See 27% more homes &amp; <span class="decoration-underline">sold history</span></li>
                                <li class="xs-mb05">Instant access to photos &amp; features</li>
                                <li>Save searches &amp; homes across devices</li>
                            </ul>
                        </section>
                        <form class="form js-signup-form js-signup-form-1" id="signup-form-1" method="post" name="signup_form">
                            <input type="hidden" class="signup-form-1-this-s-r" name="this_s_r" value="sale" />
                            <input type="hidden" class="signup-form-1-this-type" name="this_type" value="Sale" />
                            <input type="hidden" class="signup-form-1-this-state" name="this_state" value="0" />
                            <input type="hidden" class="signup-form-1-this-source" name="lead_initiation" value="signup" />
                            <div class="xs-mb3 xs-text-6 text-secondary xs-relative">
                                Enter your email address below to view property listings.<br>Learn more by viewing our <a href="/legal-privacy" class="decoration-underline text-secondary">privacy policy</a> or <a href="/contact_us.php" class="decoration-underline text-secondary">contact us</a>.
                            </div>
                            <div class="input-wrapper xs-mb1">
                                <input class="text-input rounded fill-grey-bg xs-pr5 xs-h-60" data-required="true" data-validate="true" id="email" name="emailaddress" placeholder="Email Address" type="email" required>
                                <div class="xs-flex xs-flex-align-center xs-flex-justify-center xs-border border-transparent rounded text-secondary xs-p2 xs-absolute xs-r0 xs-t0 xs-full-height">
                                    <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current text-secondary"><path d="M22 6a2 2 0 00-2-2H4a2 2 0 00-2 2v12c0 1.1.9 2 2 2h16a2 2 0 002-2V6zm-2 0l-8 5-8-5h16zm0 12H4V8l8 5 8-5v10z"/></svg>
                                </div>
                            </div>
                            <button class="button button--large fill-primary hover:fill-secondary xs-text-3 xs-full-width xs-mt2 xs-flex xs-flex-justify-center xs-flex-align-center bold shadow-2" id="submitEmail">
                                Show me the photos!
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-green xs-ml1"><circle cx="12" cy="12" r="3"></circle><path d="M20 4h-3.17l-1.24-1.35A2 2 0 0014.12 2H9.88c-.56 0-1.1.24-1.48.65L7.17 4H4a2 2 0 00-2 2v12c0 1.1.9 2 2 2h16a2 2 0 002-2V6a2 2 0 00-2-2zm-8 13a5 5 0 110-10 5 5 0 010 10z"/></svg>
                            </button>
                        </form>
                    </section>
                    <section class=" xs-hide  js-signup-page-2">
                        <section class="why-register xs-mt1 xs-mb3">
                            <h3 class="xs-text-1 heavy xs-mb3">Almost Done!</h3>
                            <p class="xs-mb3">To finish creating your account, and to see sold prices and photos, real estate boards require a name and phone number.</p>
                        </section>
                        <form class="form js-signup-form js-signup-form-2" name="signup_form" id="signup-form-2" method="post">
                            <input type="hidden" name="accept_tos" value="1">
                            <div class="input-wrapper xs-mb1">
                                <input class="text-input rounded fill-grey-bg xs-pr5 xs-h-60" type="text" placeholder="Full name" name="fullname" id="InputFullname" data-validate="true" data-required="true" required="">
                                <div class="xs-flex xs-flex-align-center xs-flex-justify-center xs-border border-transparent rounded text-secondary xs-p2 xs-absolute xs-r0 xs-t0 xs-full-height">
                                    <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current text-secondary"><path d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2m0 9c2.7 0 5.8 1.29 6 2v1H6v-.99c.2-.72 3.3-2.01 6-2.01m0-11a4 4 0 100 8 4 4 0 000-8zm0 9c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4z"/></svg>
                                </div>
                            </div>
                            <div class="input-wrapper xs-mb1">
                                <input class="text-input rounded fill-grey-bg xs-pr5 xs-h-60" type="tel" placeholder="Phone" id="mobile" name="mobile" pattern="^[\s\S]{10,}$" data-validate="true" data-required="true" required="">
                                <div class="xs-flex xs-flex-align-center xs-flex-justify-center xs-border border-transparent rounded text-secondary xs-p2 xs-absolute xs-r0 xs-t0 xs-full-height">
                                    <svg viewBox="0 0 24 24" height="20" width="20" class="fill-current text-secondary"><path d="M7 1a2 2 0 00-2 2v18c0 1.1.9 2 2 2h10a2 2 0 002-2V3a2 2 0 00-2-2H7zm0 2h10v2H7V3zm0 4h10v9H7V7zm0 11h10v3H7v-3zm3 1v1h4v-1h-4z"/></svg>
                                </div>
                            </div>
                            <button class="button button--large fill-primary hover:fill-secondary xs-text-3 xs-full-width xs-mt2 xs-flex xs-flex-justify-center xs-flex-align-center bold shadow-2" id="submitAll">
                                Create Account
                                <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-green xs-ml1" aria-hidden="true"><polygon points="15.5,5 11,5 16,12 11,19 15.5,19 20.5,12"></polygon><polygon points="8.5,5 4,5 9,12 4,19 8.5,19 13.5,12"></polygon></svg>
                            </button>
                            <div class="fill-green-highlight xs-rounded-md xs-p2 xs-mt3"><strong>Remember:</strong> Confirm your account by checking your email!</div>
                        </form>
                    </section>
                </section>

                            <section id="modal_content_privacy_and_terms" class=" xs-hide  xs-flex-column xs-max-h-full xs-overflow-hidden">
                    <iframe id="iframe-terms" class="xs-full-width js-img-defer" allowfullscreen data-img-defer-src="/privacy-policy-and-terms-of-use.php" frameborder="0" style="height: 23rem;"></iframe>
                    <footer class="gut xs-py2 xs-border-top xs-flex xs-flex-justify-center xs-flex-align-center xs-flex-shrink-0">
                        <button id="submitPrivacy" class="button button--large fill-primary hover:fill-secondary xs-full-width sm-text-3 bold rounded xs-flex xs-flex-align-center xs-flex-justify-center" type="submit" data-submit="privacy_agreement">
                            I Agree to Privacy &amp; Terms
                            <svg viewBox="0 0 24 24" height="24" width="24" class="fill-current text-green xs-ml1 xs-flex-shrink-0" aria-hidden="true"><polygon points="15.5,5 11,5 16,12 11,19 15.5,19 20.5,12"></polygon><polygon points="8.5,5 4,5 9,12 4,19 8.5,19 13.5,12"></polygon></svg>
                        </button>
                    </footer>
                </section>

        </div>
    </div>
</div>










    <script>
        function resetMapSession(){
            if(typeof(Storage) !== "undefined") {
                sessionStorage.removeItem('mapCenter');
                sessionStorage.removeItem('mapZoom');
                sessionStorage.removeItem('sarea');
            } else {
                console.log("Sorry, your browser does not support web storage...");
            }
        }
    </script>


</body>
</html>
